{"permissions": {"allow": ["Bash(ls /Users/<USER>/Desktop/Extra/Guardia/SaaS-Boilerplate-main/Guardiavision_SaaS/src/app/[locale]/(auth))", "Bash(ls /Users/<USER>/Desktop/Extra/Guardia/SaaS-Boilerplate-main/Guardiavision_SaaS/src/app/[locale]/(unauth))", "Bash(ls -la /Users/<USER>/Desktop/Extra/Guardia/SaaS-Boilerplate-main/Guardiavision_SaaS/src/app/[locale]/(auth)/dashboard/)", "Bash(cat /Users/<USER>/Desktop/Extra/Guardia/SaaS-Boilerplate-main/Guardiavision_SaaS/fix-studio-refresh-errors.sql)", "Bash(npm run build)", "<PERSON><PERSON>(cat:*)", "Bash(npm install:*)", "<PERSON><PERSON>(head:*)", "<PERSON><PERSON>(tail:*)"], "deny": [], "ask": []}}