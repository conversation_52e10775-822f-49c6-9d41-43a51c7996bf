@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import design system */
@import './design-system.css';

/* Smooth scroll animations */
.scroll-animate {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-animate.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.scroll-animate.delay-100 {
  transition-delay: 0.1s;
}

.scroll-animate.delay-200 {
  transition-delay: 0.2s;
}

.scroll-animate.delay-300 {
  transition-delay: 0.3s;
}

.scroll-animate.delay-400 {
  transition-delay: 0.4s;
}

.scroll-animate.delay-500 {
  transition-delay: 0.5s;
}

/* Grouped animations */
.scroll-animate-group .scroll-animate {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-animate-group .scroll-animate.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Float animation for particles */
@keyframes float {
  0% {
    transform: translateY(0) translateX(0);
  }
  50% {
    transform: translateY(-20px) translateX(10px);
  }
  100% {
    transform: translateY(0) translateX(0);
  }
}

/* Rotating light beam that travels around button border */
@keyframes rotate-beam {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.rainbow-border {
  position: absolute;
  inset: -2px;
  border-radius: inherit;
  padding: 2px;
  background: conic-gradient(
    from 0deg,
    transparent 0deg,
    transparent 270deg,
    rgba(74, 222, 128, 0.4) 300deg,
    rgba(74, 222, 128, 0.7) 330deg,
    rgba(34, 197, 94, 1) 0deg,
    rgba(74, 222, 128, 0.7) 30deg,
    rgba(74, 222, 128, 0.4) 60deg,
    transparent 90deg
  );
  animation: rotate-beam 2.5s linear infinite;
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

/* Login button hover effects */
.login-glow {
  mask-image: radial-gradient(ellipse, black, transparent);
  -webkit-mask-image: radial-gradient(ellipse, black, transparent);
}

.login-fill {
  mask-image: radial-gradient(ellipse at center, black 70%, transparent 100%);
  -webkit-mask-image: radial-gradient(ellipse at center, black 70%, transparent 100%);
  mask-size: 100% 100%;
  -webkit-mask-size: 100% 100%;
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  transform: scale(1.01); /* Slightly larger to ensure full coverage */
}

/* Button glow effects that extend from the button */
.blue-button-glow {
  position: relative;
  overflow: hidden;
}

.blue-button-glow::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(96, 165, 250, 0), rgba(96, 165, 250, 0.5), rgba(96, 165, 250, 0));
  background-size: 200% 100%;
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.blue-button-glow:hover::before {
  opacity: 1;
  animation: shimmer 1.5s infinite;
}

/* Purple button glow effect */
.purple-button-glow {
  position: relative;
  overflow: hidden;
}

.purple-button-glow::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(167, 139, 250, 0), rgba(167, 139, 250, 0.5), rgba(167, 139, 250, 0));
  background-size: 200% 100%;
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.purple-button-glow:hover::before {
  opacity: 1;
  animation: shimmer 1.5s infinite;
}

/* Green button glow effect */
.green-button-glow {
  position: relative;
  overflow: hidden;
}

.green-button-glow::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(74, 222, 128, 0), rgba(74, 222, 128, 0.5), rgba(74, 222, 128, 0));
  background-size: 200% 100%;
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.green-button-glow:hover::before {
  opacity: 1;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
}

/* Gold button glow effect */
.gold-button-glow:hover {
  box-shadow: 0 0 20px rgba(251, 191, 36, 0.4), 0 0 40px rgba(251, 191, 36, 0.2);
}

/* Fade-in animation for media items */
@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out forwards;
  opacity: 0; /* Start invisible */
}

/* Smooth transitions for media grid */
.media-grid-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.media-grid-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Loading shimmer effect */
@keyframes shimmer-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer-loading 1.5s infinite;
}

.dark .shimmer-loading {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200px 100%;
}



/* Simple and effective green neon border animation */
@keyframes green-glow-pulse {
  0%, 100% {
    box-shadow:
      0 0 5px rgba(74, 222, 128, 0.4),
      0 0 10px rgba(34, 197, 94, 0.3),
      0 0 15px rgba(74, 222, 128, 0.2);
  }
  50% {
    box-shadow:
      0 0 10px rgba(74, 222, 128, 0.6),
      0 0 20px rgba(34, 197, 94, 0.5),
      0 0 30px rgba(74, 222, 128, 0.4);
  }
}

@keyframes green-border-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.green-neon-border {
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
  background: linear-gradient(#4ade80, #4ade80) padding-box,
              conic-gradient(from 0deg, transparent, #22c55e, #4ade80, #22c55e, transparent) border-box;
  animation: green-glow-pulse 2s ease-in-out infinite;
}

.green-neon-border::before {
  content: "";
  position: absolute;
  inset: -2px;
  border-radius: inherit;
  padding: 2px;
  background: conic-gradient(
    from 0deg,
    transparent 0deg,
    transparent 270deg,
    rgba(34, 197, 94, 0.5) 300deg,
    rgba(74, 222, 128, 1) 330deg,
    rgba(34, 197, 94, 1) 0deg,
    rgba(74, 222, 128, 0.5) 30deg,
    transparent 90deg
  );
  animation: green-border-rotate 3s linear infinite;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
  z-index: -1;
}

/* Enhanced hover effect */
.green-neon-border:hover {
  animation: green-glow-pulse 1s ease-in-out infinite;
  transform: translateY(-1px);
  transition: transform 0.2s ease;
}

/* Accessibility: Respect reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .green-neon-border,
  .green-neon-border:hover {
    animation: none;
  }

  .green-neon-border::before {
    animation: none;
    background: linear-gradient(45deg, transparent, rgba(74, 222, 128, 0.5), transparent);
  }

  .green-neon-border:hover {
    transform: none;
    box-shadow: 0 0 10px rgba(74, 222, 128, 0.4);
  }
}
