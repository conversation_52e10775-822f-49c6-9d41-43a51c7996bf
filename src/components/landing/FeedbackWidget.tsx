'use client';

import { MessageSquare, Send, X } from 'lucide-react';
import { useState } from 'react';

export function FeedbackWidget() {
  const [isOpen, setIsOpen] = useState(false);
  const [email, setEmail] = useState('');
  const [feedback, setFeedback] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!feedback.trim() || !email.trim()) return;

    // Validate email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      // Send feedback to API endpoint
      const response = await fetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          feedback,
          timestamp: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send feedback');
      }

      setSubmitted(true);

      // Reset after 2 seconds
      setTimeout(() => {
        setEmail('');
        setFeedback('');
        setSubmitted(false);
        setIsOpen(false);
      }, 2000);
    } catch (err) {
      setError('Failed to send feedback. Please try again or email us <NAME_EMAIL>');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      {/* Feedback Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 right-6 z-40 flex items-center gap-2 rounded-full bg-green-400 px-6 py-3 font-semibold text-navy shadow-lg transition-all duration-300 hover:bg-green-500 hover:shadow-xl hover:shadow-green-400/50 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2 focus:ring-offset-navy"
        aria-label="Open feedback form"
      >
        <MessageSquare className="size-5" />
        <span className="hidden sm:inline">Feedback</span>
      </button>

      {/* Feedback Modal */}
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black/60 backdrop-blur-sm"
            onClick={() => !isSubmitting && setIsOpen(false)}
            aria-hidden="true"
          />

          {/* Modal Content */}
          <div className="relative w-full max-w-lg rounded-2xl border border-navy-light bg-navy p-6 shadow-2xl">
            {/* Header */}
            <div className="mb-4 flex items-center justify-between">
              <h3 className="text-2xl font-bold text-white">Share Your Feedback</h3>
              <button
                onClick={() => setIsOpen(false)}
                className="rounded-lg p-2 text-gray-400 transition-colors hover:bg-navy-light hover:text-white focus:outline-none focus:ring-2 focus:ring-green-400"
                aria-label="Close feedback form"
                disabled={isSubmitting}
              >
                <X className="size-5" />
              </button>
            </div>

            {/* Success Message */}
            {submitted ? (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="mb-4 flex size-16 items-center justify-center rounded-full bg-green-400/20">
                  <svg
                    className="size-8 text-green-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
                <p className="text-lg font-semibold text-white">Thank you for your feedback!</p>
                <p className="mt-2 text-sm text-gray-300">We appreciate your input.</p>
              </div>
            ) : (
              <form onSubmit={handleSubmit}>
                {/* Description */}
                <p className="mb-4 text-gray-300">
                  We'd love to hear your thoughts on Guardiavision. Your feedback helps us improve!
                </p>

                {/* Error Message */}
                {error && (
                  <div className="mb-4 rounded-lg border border-red-400 bg-red-400/10 px-4 py-3 text-sm text-red-400">
                    {error}
                  </div>
                )}

                {/* Email Input */}
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Your email address"
                  className="mb-4 w-full rounded-lg border border-navy-light bg-navy-dark px-4 py-3 text-white placeholder-gray-400 transition-colors focus:border-green-400 focus:outline-none focus:ring-2 focus:ring-green-400/50"
                  disabled={isSubmitting}
                  required
                  aria-label="Email address"
                />

                {/* Textarea */}
                <textarea
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  placeholder="Tell us what you think... What features would you like to see? What can we improve?"
                  className="mb-4 w-full resize-none rounded-lg border border-navy-light bg-navy-dark px-4 py-3 text-white placeholder-gray-400 transition-colors focus:border-green-400 focus:outline-none focus:ring-2 focus:ring-green-400/50"
                  rows={5}
                  disabled={isSubmitting}
                  required
                  maxLength={500}
                  aria-label="Feedback message"
                />

                {/* Footer */}
                <div className="flex items-center justify-between">
                  <p className="text-xs text-gray-400">
                    {feedback.length}/500 characters
                  </p>
                  <button
                    type="submit"
                    disabled={isSubmitting || !feedback.trim() || !email.trim()}
                    className="inline-flex items-center gap-2 rounded-lg bg-green-400 px-6 py-3 font-semibold text-navy transition-all duration-300 hover:bg-green-500 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2 focus:ring-offset-navy disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="size-5 animate-spin" viewBox="0 0 24 24">
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                            fill="none"
                          />
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          />
                        </svg>
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send className="size-5" />
                        Send Feedback
                      </>
                    )}
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}
    </>
  );
}
