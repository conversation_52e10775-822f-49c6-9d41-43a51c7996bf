'use client';

import { useState, useRef, useEffect } from 'react';
import { ArrowLeft, Zap, Plus, LogOut, Settings, LayoutGrid, Eye, EyeOff, Trash2, Target, Loader2, ChevronLeft, ChevronRight } from 'lucide-react';
import { setTimeout } from 'timers';
import { useUser, useClerk } from '@clerk/nextjs';
import { useSupabaseClient } from '@/utils/supabase/client';
import { InsufficientCreditsModal } from './InsufficientCreditsModal';
import GuardiaVisionVideoStudio from './GuardiaVisionVideoStudio';

// API Configuration - Updated GuardiaVision MASA API endpoint
const API_BASE_URL = 'https://guardiavision-prod-473899915275.europe-west1.run.app';
// Fallback URL in case primary fails
const FALLBACK_API_URL = 'https://guardiavision-prod-gpu-473899915275.europe-west1.run.app';

interface DetectedObject {
  id: number;
  label: string;
  confidence: number;
  selected: boolean;
  color: string;
  bbox?: [number, number, number, number];
  prompt?: string; // Store which prompt detected this object
}

interface MediaFile {
  id: string | null;
  original_filename: string;
  file_path: string;
  file_type: string;
  file_size: number;
  mime_type?: string;
  metadata?: {
    originalUrl: string;
  };
  created_at: string;
  upload_date?: string;
  user_id?: string;
}

interface GuardiaVisionStudioProps {
  mediaFile: MediaFile;
  originalImageUrl: string;
  onBack: () => void;
}

export default function GuardiaVisionStudio({ mediaFile, originalImageUrl, onBack }: GuardiaVisionStudioProps) {
  // Check if this is a video file
  const isVideoFile = mediaFile.file_type?.includes('video') || mediaFile.mime_type?.startsWith('video/');

  // If it's a video file, render the video studio instead
  if (isVideoFile) {
    return <GuardiaVisionVideoStudio mediaFile={mediaFile} originalVideoUrl={originalImageUrl} onBack={onBack} />;
  }
  // User authentication and data
  const { user } = useUser();
  const { signOut } = useClerk();
  const supabase = useSupabaseClient();

  // UI state
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState(false);

  // Multi-image navigation state
  const [allUserImages, setAllUserImages] = useState<MediaFile[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [loadingImages, setLoadingImages] = useState(true);

  // Draggable panel state
  const [panelPosition, setPanelPosition] = useState({ x: 0, y: 0 }); // Start at bottom center
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  // Image processing states
  const [imagePreview, setImagePreview] = useState<string>('');
  const [processedImageUrl, setProcessedImageUrl] = useState<string>('');
  const [detectedObjects, setDetectedObjects] = useState<DetectedObject[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [imageLoadError, setImageLoadError] = useState<string | null>(null);
  const [isLoadingImage, setIsLoadingImage] = useState(true);

  // Insufficient credits modal state
  const [showInsufficientCreditsModal, setShowInsufficientCreditsModal] = useState(false);
  const [insufficientCreditsData, setInsufficientCreditsData] = useState({ current: 0, required: 3 });


  // Detection categories (matching the screenshot)
  const [detectionCategories, setDetectionCategories] = useState({
    fullScreenBlur: false,
    customBlur: false,
    enableAIDetection: true,
    fullBody: false,
    face: true,
    vehicle: false,
    licensePlate: false
  });

  // Blur controls
  const [blurIntensity, setBlurIntensity] = useState(50); // Higher default for more visible effects
  const [pendingBlurIntensity, setPendingBlurIntensity] = useState(50); // For debounced intensity
  const [blurType, setBlurType] = useState<'blur' | 'pixelate'>('blur');
  const [customBlurAreas, setCustomBlurAreas] = useState<Array<{id: string, x: number, y: number, width: number, height: number, visible: boolean}>>([]);
  const [textPrompts, setTextPrompts] = useState<string[]>(['']); // Support multiple prompts
  const [hasUserTyped, setHasUserTyped] = useState<boolean>(false); // Track if user has typed
  const [intensityDebounceTimer, setIntensityDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  // Confidence filtering
  const [confidenceThreshold, setConfidenceThreshold] = useState<number>(0.2); // Default 20%
  const [allDetectedObjects, setAllDetectedObjects] = useState<DetectedObject[]>([]); // Store all objects from API
  const [lastUsedPrompt, setLastUsedPrompt] = useState<string>(''); // Store the last prompt used for detection
  const [apiStatus, setApiStatus] = useState<'checking' | 'online' | 'offline'>('checking');

  // Image display controls
  const [imageZoomMode, setImageZoomMode] = useState<'fit' | 'actual'>('fit'); // Default to fit-to-container


  const imageRef = useRef<HTMLImageElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Fetch all user images for navigation
  const fetchAllUserImages = async () => {
    if (!user?.id) return;

    try {
      setLoadingImages(true);
      const response = await fetch('/api/user/files?type=image&limit=100');
      const result = await response.json();

      if (result.success && Array.isArray(result.files)) {
        // Filter out GIFs since they're handled as videos now
        const imageFiles = result.files.filter((file: any) =>
          file.file_type === 'image' &&
          !file.mime_type?.includes('gif') &&
          !file.original_filename?.toLowerCase().endsWith('.gif')
        );

        setAllUserImages(imageFiles);

        // Find current image index
        const currentIndex = imageFiles.findIndex((img: any) => img.id === mediaFile.id);
        setCurrentImageIndex(currentIndex >= 0 ? currentIndex : 0);
      }
    } catch (error) {
      console.error('Failed to fetch user images:', error);
    } finally {
      setLoadingImages(false);
    }
  };

  // Navigate to different image
  const navigateToImage = async (imageFile: MediaFile) => {
    if (imageFile.id === mediaFile.id) return; // Already viewing this image

    try {
      // Clear current state first
      setIsLoadingImage(true);
      setImageLoadError(null);
      setError(null);

      // Update URL without reload
      const newUrl = `/studio?file=${imageFile.id}`;
      window.history.pushState({}, '', newUrl);

      // Update current image data
      const currentIndex = allUserImages.findIndex(img => img.id === imageFile.id);
      setCurrentImageIndex(currentIndex >= 0 ? currentIndex : 0);

      // Load new image - use 'url' field which contains the signed URL from Supabase
      let imageUrl = '';
      if ((imageFile as any).url) {
        // Priority 1: Use the signed URL from the API response
        imageUrl = (imageFile as any).url;
        console.log('✅ Using signed URL for navigation:', imageUrl);
      } else if (imageFile.metadata?.originalUrl) {
        // Priority 2: Use metadata originalUrl
        imageUrl = imageFile.metadata.originalUrl;
        console.log('✅ Using metadata URL for navigation:', imageUrl);
      } else {
        // Priority 3: Fetch secure URL from API
        try {
          const response = await fetch(`/api/files/${imageFile.id}`, { cache: 'no-store' });
          if (response.ok) {
            const data = await response.json();
            if (data.success && data.file?.secureUrl) {
              imageUrl = data.file.secureUrl;
              console.log('✅ Got secure URL from API for navigation:', imageUrl);
            } else {
              throw new Error('No secure URL in API response');
            }
          } else {
            throw new Error('Failed to fetch secure URL');
          }
        } catch (apiError) {
          console.error('❌ Failed to load secure URL:', apiError);
          setError('Unable to load image');
          setIsLoadingImage(false);
          return;
        }
      }

      setImagePreview(imageUrl);

      // Clear previous processing state
      setProcessedImageUrl('');
      setDetectedObjects([]);
      setAllDetectedObjects([]);

      // Load saved state for new image
      loadSavedState();

    } catch (error) {
      console.error('Navigation error:', error);
      setError('Failed to load image');
    }
  };

  // Handle dragging for image navigation panel
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent text selection
    e.stopPropagation(); // Prevent event bubbling
    
    setIsDragging(true);
    setDragOffset({
      x: e.clientX - panelPosition.x,
      y: e.clientY - panelPosition.y
    });
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;
    
    e.preventDefault(); // Prevent any default behavior
    
    setPanelPosition({
      x: e.clientX - dragOffset.x,
      y: e.clientY - dragOffset.y
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Add mouse event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragOffset]);

  // Handle file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      console.log('📁 File uploaded:', file.name, file.size, 'bytes');

      // Convert to base64 for preview
      const reader = new FileReader();
      reader.onload = () => {
        const base64String = reader.result as string;
        setImagePreview(base64String);
        setImageLoadError(null);
        setIsLoadingImage(false);
        console.log('✅ File converted to base64 for preview');
      };
      reader.readAsDataURL(file);
    } else {
      setError('Please select a valid image file (JPG, PNG, GIF, WebP)');
    }
  };

  // User dropdown functions
  const handleWorkspaceClick = () => {
    window.location.href = '/dashboard';
  };

  const handleAccountSettings = () => {
    window.location.href = '/dashboard/user-profile';
  };

  const handleSignOut = async () => {
    await signOut();
    window.location.href = '/';
  };

  // Get user initials for avatar
  const getUserInitials = () => {
    if (!user) return 'U';

    const firstName = user.firstName || '';
    const lastName = user.lastName || '';

    if (firstName && lastName && firstName.length > 0 && lastName.length > 0) {
      return `${firstName[0] || ''}${lastName[0] || ''}`.toUpperCase();
    } else if (firstName && firstName.length > 0) {
      return (firstName[0] || '').toUpperCase() || 'U';
    } else if (user.emailAddresses?.[0]?.emailAddress) {
      const email = user.emailAddresses[0].emailAddress;
      return email && email.length > 0 ? (email[0] || '').toUpperCase() || 'U' : 'U';
    }

    return 'U';
  };

  // Save/Download functions
  const handleDoneClick = () => {
    setShowSaveDialog(true);
  };

  const handleDownloadOnly = () => {
    if (processedImageUrl) {
      // Create download link
      const link = document.createElement('a');
      link.href = processedImageUrl;
      link.download = `processed_${mediaFile.original_filename}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
    setShowSaveDialog(false);
  };

  const handleSaveAndDownload = async () => {
    try {
      // First download the image
      handleDownloadOnly();

      // Convert blob URL to base64 for persistent storage
      let processedImageBase64 = '';
      if (processedImageUrl && processedImageUrl.startsWith('blob:')) {
        try {
          const response = await fetch(processedImageUrl);
          const blob = await response.blob();
          const reader = new FileReader();

          processedImageBase64 = await new Promise((resolve) => {
            reader.onload = () => resolve(reader.result as string);
            reader.readAsDataURL(blob);
          });

          console.log('✅ Converted processed image to base64 for storage');
        } catch (error) {
          console.error('❌ Failed to convert processed image to base64:', error);
          processedImageBase64 = processedImageUrl; // Fallback to original URL
        }
      } else {
        processedImageBase64 = processedImageUrl;
      }

      // Get all unique prompts used
      const uniquePrompts = [...new Set(allDetectedObjects.map(obj => obj.prompt).filter(Boolean))];

      // Save comprehensive processing state with full history
      const saveData = {
        original_file_id: mediaFile.id,
        processed_image_url: processedImageBase64,
        processing_settings: {
          // Current settings
          blur_intensity: blurIntensity,
          blur_type: blurType,
          confidence_threshold: confidenceThreshold,

          // All detection history
          all_detected_objects: allDetectedObjects, // Complete detection history
          filtered_detected_objects: detectedObjects, // Currently visible objects
          unique_prompts_used: uniquePrompts, // All prompts used
          last_used_prompt: lastUsedPrompt,

          // Legacy fields for compatibility
          custom_blur_areas: customBlurAreas,
          detection_categories: detectionCategories,

          // Processing history metadata
          total_detections: allDetectedObjects.length,
          visible_detections: detectedObjects.length,
          processing_timestamp: new Date().toISOString()
        },
        file_name: `processed_${mediaFile.original_filename}`,
        created_at: new Date().toISOString()
      };

      console.log('💾 Saving comprehensive processing state:', {
        totalObjects: allDetectedObjects.length,
        visibleObjects: detectedObjects.length,
        uniquePrompts: uniquePrompts.length,
        settings: {
          blurIntensity,
          blurType,
          confidenceThreshold
        }
      });

      // Save to database
      const response = await fetch('/api/studio/save-processing', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(saveData)
      });

      if (response.ok) {
        console.log('✅ Comprehensive processing state saved successfully');
      } else {
        console.error('❌ Failed to save processing state');
      }

      // Mark file as completed after successful save
      await markFileAsCompleted();
    } catch (error) {
      console.error('❌ Error saving processing state:', error);
    }

    setShowSaveDialog(false);
  };

  // Mark file as completed when saved from studio
  const markFileAsCompleted = async () => {
    if (!mediaFile.id) return;

    try {
      console.log(`✅ Marking file ${mediaFile.id} as completed...`);

      const response = await fetch('/api/user/files/update-status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileId: mediaFile.id,
          status: 'completed'
        })
      });

      if (response.ok) {
        console.log('✅ File marked as completed successfully');
      } else {
        console.error('❌ Failed to mark file as completed:', await response.text());
      }
    } catch (error) {
      console.error('❌ Error marking file as completed:', error);
    }
  };



  // Detected objects management
  const toggleObjectVisibility = (objectId: number) => {
    console.log('👁️ Toggling visibility for object:', objectId);

    setDetectedObjects(prev =>
      prev.map(obj =>
        obj.id === objectId
          ? { ...obj, selected: !obj.selected }
          : obj
      )
    );

    // Auto-save handled by useEffect

    // Re-apply blur only to visible objects
    setTimeout(() => {
      const visibleObjects = detectedObjects.map(obj =>
        obj.id === objectId ? { ...obj, selected: !obj.selected } : obj
      ).filter(obj => obj.selected);

      console.log(`🔄 Re-applying blur to ${visibleObjects.length} visible objects after visibility toggle`);

      if (visibleObjects.length > 0 && processedImageUrl) {
        console.log('🎯 Re-blurring visible objects only');
        reapplyBlurToRemainingObjects();
      } else if (visibleObjects.length === 0) {
        console.log('🧹 No visible objects, resetting to original image');
        setProcessedImageUrl('');
      }
    }, 100); // Small delay to ensure state is updated
  };

  const deleteDetectedObject = (objectId: number) => {
    console.log('🗑️ Deleting object:', objectId, 'and re-applying blur to remaining objects');

    // Remove from both arrays
    setAllDetectedObjects(prev => prev.filter(obj => obj.id !== objectId));
    setDetectedObjects(prev => prev.filter(obj => obj.id !== objectId));

    // Auto-save handled by useEffect

    // IMPORTANT: Re-apply blur to remaining objects only
    // This ensures the deleted object's blur effect is removed
    setTimeout(() => {
      const remainingObjects = detectedObjects.filter(obj => obj.id !== objectId);
      console.log(`🔄 Re-applying blur to ${remainingObjects.length} remaining objects after deletion`);

      if (remainingObjects.length > 0) {
        // Re-blur only the remaining objects (not the deleted one)
        console.log('🎯 Re-blurring remaining objects to remove deleted object blur');
        reapplyBlurToRemainingObjects();
      } else {
        // No objects left, reset to original image
        console.log('🧹 No objects left, resetting to original image');
        setProcessedImageUrl('');
      }
    }, 100); // Small delay to ensure state is updated

    console.log('✅ Object deleted and blur effects updated');
  };

  // Toggle visibility of custom blur areas
  const toggleCustomAreaVisibility = (areaId: string) => {
    console.log('👁️ Toggling visibility for custom area:', areaId);

    setCustomBlurAreas(prev =>
      prev.map(area =>
        area.id === areaId
          ? { ...area, visible: !area.visible }
          : area
      )
    );

    // Auto-save handled by useEffect
  };

  // Check if there are any blurrable objects (detected objects OR custom areas)
  const hasBlurrableObjects = () => {
    const visibleDetectedObjects = detectedObjects.filter(obj => obj.selected);
    const visibleCustomAreas = customBlurAreas.filter(area => area.visible);
    return visibleDetectedObjects.length > 0 || visibleCustomAreas.length > 0;
  };

  // Helper functions for multiple prompts
  const getActivePrompts = () => textPrompts.filter(prompt => prompt.trim().length > 0);
  const getAllPromptsText = () => getActivePrompts().join(', ');

  // Check if detect button should be enabled (at least one prompt is not empty)
  const canDetect = () => {
    return textPrompts.some(prompt => prompt.trim().length > 0);
  };

  // Add new prompt
  const addNewPrompt = () => {
    setTextPrompts(prev => [...prev, '']);
  };

  // Remove prompt at index
  const removePrompt = (index: number) => {
    if (textPrompts.length > 1) {
      setTextPrompts(prev => prev.filter((_, i) => i !== index));
    }
  };

  // Update prompt at index
  const updatePrompt = (index: number, value: string) => {
    setTextPrompts(prev => prev.map((prompt, i) => i === index ? value : prompt));
    setHasUserTyped(true);
  };

  // Debounced intensity change handler
  const handleIntensityChange = (newIntensity: number) => {
    setPendingBlurIntensity(newIntensity);

    // Clear existing timer
    if (intensityDebounceTimer) {
      clearTimeout(intensityDebounceTimer);
    }

    // Set new timer for 2 seconds
    const timer = setTimeout(() => {
      setBlurIntensity(newIntensity);
      // Apply the intensity change immediately to all existing blurred areas
      if (detectedObjects.length > 0 || customBlurAreas.length > 0) {
        applyBlurToAllObjects();
      }
    }, 2000);

    setIntensityDebounceTimer(timer);
  };

  // Immediate blur/pixelate toggle effect - FIXED: Only affects explicitly selected objects
  const handleBlurTypeToggle = (newType: 'blur' | 'pixelate') => {
    console.log(`🔄 Switching blur type from ${blurType} to ${newType}`);

    // Get ONLY the explicitly selected objects and visible custom areas
    const selectedDetectedObjects = detectedObjects.filter(obj => obj.selected === true);
    const visibleCustomAreas = customBlurAreas.filter(area => area.visible === true);

    console.log(`🎯 Effect will apply to:`);
    console.log(`   - Selected detected objects: ${selectedDetectedObjects.length}`);
    console.log(`   - Visible custom areas: ${visibleCustomAreas.length}`);
    console.log(`   - Selected objects:`, selectedDetectedObjects.map(obj => `${obj.label} (ID: ${obj.id})`));

    setBlurType(newType);

    // Only apply if there are explicitly selected/visible objects
    if (selectedDetectedObjects.length > 0 || visibleCustomAreas.length > 0) {
      console.log(`✅ Applying ${newType} effect to ${selectedDetectedObjects.length + visibleCustomAreas.length} explicitly selected objects/areas`);
      applyBlurToSelectedObjects();
    } else {
      console.log(`⚠️ No explicitly selected objects or visible custom areas - no effect applied`);
    }
  };

  // Apply blur to ONLY explicitly selected objects (prevents bleeding bug)
  const applyBlurToSelectedObjects = async () => {
    if (!imagePreview) return;

    // Get ONLY explicitly selected objects and visible custom areas
    const selectedDetectedObjects = detectedObjects.filter(obj => obj.selected === true);
    const visibleCustomAreas = customBlurAreas.filter(area => area.visible === true);

    if (selectedDetectedObjects.length === 0 && visibleCustomAreas.length === 0) {
      console.log('🧹 No selected objects or visible areas - clearing processed image');
      setProcessedImageUrl('');
      return;
    }

    console.log(`🎯 Applying blur to ONLY selected objects:`);
    console.log(`   - Selected detected objects: ${selectedDetectedObjects.map(obj => `${obj.label} (ID: ${obj.id})`)}`);
    console.log(`   - Visible custom areas: ${visibleCustomAreas.map(area => `Area ${area.id}`)}`);

    setIsProcessing(true);
    try {
      await blurAllDetectedObjects(); // This function already filters for selected objects
    } catch (error) {
      console.error('Error applying blur to selected objects:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Apply blur to all objects (both detected and custom) - FIXED: Only selected objects
  const applyBlurToAllObjects = async () => {
    console.log('🎯 applyBlurToAllObjects called - delegating to applyBlurToSelectedObjects');
    await applyBlurToSelectedObjects();
  };

  // Re-apply blur only to remaining objects (after deletion)
  const reapplyBlurToRemainingObjects = async () => {
    if (!imagePreview) {
      console.error('❌ No original image available for re-blurring');
      return;
    }

    if (detectedObjects.length === 0) {
      console.log('🧹 No remaining objects, resetting to original image');
      setProcessedImageUrl('');
      return;
    }

    console.log(`🔄 Re-applying blur to ${detectedObjects.length} remaining objects`);
    console.log('📋 Remaining objects:', detectedObjects.map(obj => `${obj.label} (ID: ${obj.id})`));

    setIsProcessing(true);
    setError(null);

    try {
      // Get all unique prompts from remaining objects
      const uniquePrompts = [...new Set(detectedObjects.map(obj => obj.prompt).filter(Boolean))];
      const promptToUse = uniquePrompts.join(' ') || lastUsedPrompt || getAllPromptsText();

      if (!promptToUse) {
        console.error('❌ No prompt available for re-blurring');
        setError('No detection prompt available');
        return;
      }

      console.log('🎯 Re-blurring with prompt:', promptToUse);

      const formData = new FormData();

      // Convert original image to blob (not the processed one)
      const imageResponse = await fetch(imagePreview);
      const imageBlob = await imageResponse.blob();

      formData.append('image', imageBlob, 'image.jpg');
      formData.append('text_prompt', promptToUse);
      formData.append('score_threshold', confidenceThreshold.toString());
      formData.append('visualize', 'false');
      formData.append('blur_detections', 'true');
      formData.append('blur_strength', blurIntensity.toString());
      formData.append('blur_type', blurType);

      console.log('📤 Re-applying blur to remaining objects only');

      const response = await tryApiCall(formData, 'Re-blur Remaining Objects');

      // Handle response
      const contentType = response.headers.get('content-type');

      if (contentType && contentType.startsWith('image/')) {
        const blob = await response.blob();
        console.log('📷 Received re-blurred image, size:', blob.size, 'bytes');

        if (blob.size > 0) {
          const imageUrl = URL.createObjectURL(blob);
          setProcessedImageUrl(imageUrl);
          console.log('✅ Successfully re-applied blur to remaining objects only!');
        } else {
          throw new Error('Received empty image blob');
        }
      } else {
        const text = await response.text();
        console.error('❌ Unexpected response format:', text);
        throw new Error(`Unexpected response format: ${contentType}`);
      }

    } catch (error) {
      console.error('❌ Failed to re-apply blur to remaining objects:', error);
      setError(`Failed to re-apply blur: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Filter detected objects based on confidence threshold
  const filterObjectsByConfidence = (threshold: number) => {
    const filteredObjects = allDetectedObjects.filter(obj => obj.confidence >= threshold * 100);
    console.log(`🎯 Filtering: ${allDetectedObjects.length} total objects, threshold: ${threshold * 100}%`);
    console.log(`🎯 Objects passing filter: ${filteredObjects.length}`);
    console.log(`🎯 Filtered objects:`, filteredObjects.map(obj => `${obj.label} (${obj.confidence}%)`));

    setDetectedObjects(filteredObjects);

    // Force a re-render (auto-save handled by useEffect)
    setTimeout(() => {
      console.log(`🔄 State updated: detectedObjects now has ${filteredObjects.length} objects`);
    }, 100);
  };

  // Update confidence threshold and filter objects
  const updateConfidenceThreshold = (newThreshold: number) => {
    setConfidenceThreshold(newThreshold);
    console.log('🎚️ Confidence threshold updated to:', newThreshold);

    // Filter objects immediately
    if (allDetectedObjects.length > 0) {
      filterObjectsByConfidence(newThreshold);
    }

    // Auto-save handled by useEffect
  };

  // Real-time blur intensity update with optimized debouncing
  const updateBlurIntensity = (newIntensity: number) => {
    console.log('🎚️ INTENSITY SLIDER MOVED!');
    console.log('📊 Slider state:', {
      oldIntensity: blurIntensity,
      newIntensity,
      processedImageUrl: !!processedImageUrl,
      detectedObjects: detectedObjects.length
    });

    setBlurIntensity(newIntensity);

    // Auto-save handled by useEffect

    // Re-apply blur if there's already a processed image AND (detected objects OR full screen blur OR custom areas)
    const hasDetectedObjects = detectedObjects.length > 0;
    const hasCustomAreas = customBlurAreas.filter(area => area.visible).length > 0;
    const isFullScreenBlur = detectionCategories.fullScreenBlur;
    const shouldReapplyBlur = processedImageUrl && (hasDetectedObjects || hasCustomAreas || isFullScreenBlur);

    if (shouldReapplyBlur) {
      console.log('✅ Conditions met for re-applying blur:', {
        hasProcessedImage: !!processedImageUrl,
        hasDetectedObjects,
        hasCustomAreas,
        isFullScreenBlur
      });

      // Clear any existing timeout
      if ((window as any).blurIntensityTimeout) {
        clearTimeout((window as any).blurIntensityTimeout);
      }

      // Re-apply blur with new intensity
      (window as any).blurIntensityTimeout = setTimeout(() => {
        console.log('🔄 TIMEOUT TRIGGERED - Re-applying blur with intensity:', newIntensity);

        if (isFullScreenBlur) {
          console.log('🌀 Re-applying FULL SCREEN blur with new intensity');
          applyFullScreenBlur();
        } else {
          console.log('🎯 Re-applying OBJECT/CUSTOM blur with new intensity');
          blurAllDetectedObjects();
        }
      }, 300);
    } else {
      console.log('❌ Conditions NOT met for re-applying blur:', {
        hasProcessedImage: !!processedImageUrl,
        hasDetectedObjects,
        hasCustomAreas,
        isFullScreenBlur
      });
    }
  };

  // Auto-save current state to localStorage and database
  const autoSaveState = async () => {
    if (!mediaFile.id) return;

    try {
      const studioState = {
        mediaFileId: mediaFile.id,
        detectedObjects: detectedObjects,
        allDetectedObjects: allDetectedObjects,
        customBlurAreas: customBlurAreas,
        textPrompts: textPrompts,
        lastUsedPrompt: lastUsedPrompt,
        blurIntensity: blurIntensity,
        blurType: blurType,
        confidenceThreshold: confidenceThreshold,
        detectionCategories: detectionCategories,
        processedImageUrl: processedImageUrl,
        hasUserTyped: hasUserTyped,
        timestamp: Date.now()
      };

      // Save to localStorage for immediate persistence
      const storageKey = `studio_state_${mediaFile.id}`;
      localStorage.setItem(storageKey, JSON.stringify(studioState));
      console.log('💾 Studio state saved to localStorage:', storageKey);
      console.log('💾 Saved state contains:', {
        detectedObjects: studioState.detectedObjects.length,
        allDetectedObjects: studioState.allDetectedObjects.length,
        customBlurAreas: studioState.customBlurAreas.length,
        textPrompts: studioState.textPrompts,
        hasUserTyped: studioState.hasUserTyped
      });

      // Also save to database (non-blocking)
      try {
        // Only save to database occasionally to reduce API calls
        const shouldSaveToDb = Math.random() < 0.3; // 30% chance to reduce frequency

        if (shouldSaveToDb) {
          const response = await fetch('/api/studio/save-state', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(studioState),
          });

          if (response.ok) {
            const result = await response.json();
            console.log('💾 Studio state saved:', result.database_saved ? 'database + localStorage' : 'localStorage only');
          } else {
            console.log('💾 Database save failed - localStorage saved');
          }
        } else {
          console.log('💾 Database save skipped - localStorage saved');
        }
      } catch (dbError) {
        // CRITICAL FIX: Completely silent database errors
        console.log('💾 Database save failed - localStorage saved');
      }
    } catch (error) {
      console.error('❌ Auto-save failed:', error);
    }
  };

  // Load saved state from localStorage
  const loadSavedState = () => {
    if (!mediaFile.id) return;

    try {
      const storageKey = `studio_state_${mediaFile.id}`;
      const savedState = localStorage.getItem(storageKey);

      if (savedState) {
        const state = JSON.parse(savedState);
        console.log('📂 Loading saved studio state:', {
          detectedObjects: state.detectedObjects?.length || 0,
          allDetectedObjects: state.allDetectedObjects?.length || 0,
          customBlurAreas: state.customBlurAreas?.length || 0,
          textPrompts: state.textPrompts,
          hasUserTyped: state.hasUserTyped
        });

        // Restore state with proper checks
        if (state.detectedObjects && Array.isArray(state.detectedObjects)) {
          setDetectedObjects(state.detectedObjects);
          console.log('📂 Restored detectedObjects:', state.detectedObjects.length);
        }
        if (state.allDetectedObjects && Array.isArray(state.allDetectedObjects)) {
          setAllDetectedObjects(state.allDetectedObjects);
          console.log('📂 Restored allDetectedObjects:', state.allDetectedObjects.length);
        }
        if (state.customBlurAreas && Array.isArray(state.customBlurAreas)) {
          setCustomBlurAreas(state.customBlurAreas);
          console.log('📂 Restored customBlurAreas:', state.customBlurAreas.length);
        }
        if (state.textPrompts && Array.isArray(state.textPrompts)) setTextPrompts(state.textPrompts);
        if (state.lastUsedPrompt) setLastUsedPrompt(state.lastUsedPrompt);
        if (state.blurIntensity) setBlurIntensity(state.blurIntensity);
        if (state.blurType) setBlurType(state.blurType);
        if (state.confidenceThreshold) setConfidenceThreshold(state.confidenceThreshold);
        if (state.detectionCategories) setDetectionCategories(state.detectionCategories);
        if (state.processedImageUrl) setProcessedImageUrl(state.processedImageUrl);
        if (state.hasUserTyped !== undefined) setHasUserTyped(state.hasUserTyped);

        console.log('✅ Studio state restored successfully');
      } else {
        console.log('📂 No saved state found for this media file');
      }
    } catch (error) {
      console.error('❌ Failed to load saved state:', error);
    }
  };

  // Mark file as draft when studio opens
  const markFileAsDraft = async () => {
    if (!mediaFile.id) return;

    try {
      console.log(`📋 Marking file ${mediaFile.id} as draft...`);

      const response = await fetch('/api/user/files/update-status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileId: mediaFile.id,
          status: 'draft'
        })
      });

      if (response.ok) {
        console.log('✅ File marked as draft successfully');
      } else {
        console.error('❌ Failed to mark file as draft:', await response.text());
      }
    } catch (error) {
      console.error('❌ Error marking file as draft:', error);
    }
  };

  // Load saved state when component mounts or media file changes
  useEffect(() => {
    if (mediaFile.id) {
      loadSavedState();
      // Mark file as draft when opened in studio
      markFileAsDraft();
    }
  }, [mediaFile.id]);

  // Auto-save with reduced frequency to prevent API spam
  useEffect(() => {
    if (mediaFile.id) {
      console.log('🔄 State changed - scheduling auto-save');
      const timeoutId = setTimeout(() => {
        autoSaveState();
      }, 2000); // Increased delay to reduce API calls

      return () => clearTimeout(timeoutId);
    }
  }, [
    detectedObjects,
    allDetectedObjects,
    customBlurAreas,
    textPrompts,
    lastUsedPrompt,
    blurIntensity,
    blurType,
    confidenceThreshold,
    detectionCategories,
    processedImageUrl,
    hasUserTyped,
    mediaFile.id
  ]);

  // Check and deduct credits using dashboard approach
  const checkAndDeductCredits = async (creditsNeeded: number = 3): Promise<boolean> => {
    if (!user?.id) {
      setError('User not authenticated');
      return false;
    }

    try {
      // Check current credits using same method as dashboard
      const { data: userData, error: fetchError } = await supabase
        .from('users')
        .select('credits, subscription_type')
        .eq('id', user.id)
        .single();

      if (fetchError || !userData) {
        console.error('❌ Failed to fetch user credits:', fetchError);
        setError('Failed to check credits. Please try again.');
        return false;
      }

      const currentCredits = userData.credits || 0;
      const requiredCredits = creditsNeeded;

      console.log(`💰 User has ${currentCredits} credits, needs ${requiredCredits}`);

      // Check if user has enough credits
      if (currentCredits < requiredCredits) {
        console.log(`❌ Insufficient credits: has ${currentCredits}, needs ${requiredCredits}`);
        setInsufficientCreditsData({ current: currentCredits, required: requiredCredits });
        setShowInsufficientCreditsModal(true);
        return false;
      }

      // Deduct credits directly in Supabase
      const newCredits = currentCredits - requiredCredits;
      const { error: updateError } = await supabase
        .from('users')
        .update({
          credits: newCredits,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);

      if (updateError) {
        console.error('❌ Failed to deduct credits:', updateError);
        setError('Failed to deduct credits. Please try again.');
        return false;
      }

      console.log(`✅ Successfully deducted ${requiredCredits} credits. Remaining: ${newCredits}`);
      return true;

    } catch (error) {
      console.error('❌ Error in credit check/deduction:', error);
      setError('Error processing credits. Please try again.');
      return false;
    }
  };

  // Clear all detections
  const clearAllDetections = () => {
    setAllDetectedObjects([]);
    setDetectedObjects([]);
    setProcessedImageUrl('');
    setLastUsedPrompt('');
    console.log('🧹 Cleared all detections');

    // Auto-save handled by useEffect
  };

  // Add custom blur area with 4-digit random ID
  const addCustomBlurArea = () => {
    if (!imageRef.current) return;

    const img = imageRef.current;

    // Generate 4-digit random ID
    const randomId = Math.floor(1000 + Math.random() * 9000);

    // Create a new custom blur area in the center of the image
    const centerX = img.naturalWidth * 0.4;
    const centerY = img.naturalHeight * 0.4;
    const width = img.naturalWidth * 0.2;
    const height = img.naturalHeight * 0.2;

    const newArea = {
      id: randomId.toString(),
      x: centerX,
      y: centerY,
      width: width,
      height: height,
      visible: true // Default to visible
    };

    setCustomBlurAreas(prev => [...prev, newArea]);
    console.log('➕ Added custom blur area with ID:', randomId);

    // Auto-save handled by useEffect
  };

  // Handle dragging custom blur areas
  const handleCustomAreaDrag = (areaId: string, startX: number, startY: number, e: React.MouseEvent) => {
    e.preventDefault();

    const img = imageRef.current;
    if (!img) return;

    const imgRect = img.getBoundingClientRect();
    const scaleX = img.naturalWidth / imgRect.width;
    const scaleY = img.naturalHeight / imgRect.height;

    const startMouseX = e.clientX;
    const startMouseY = e.clientY;

    const handleMouseMove = (moveEvent: MouseEvent) => {
      const deltaX = (moveEvent.clientX - startMouseX) * scaleX;
      const deltaY = (moveEvent.clientY - startMouseY) * scaleY;

      setCustomBlurAreas(prev => prev.map(area =>
        area.id === areaId
          ? {
              ...area,
              x: Math.max(0, Math.min(img.naturalWidth - area.width, startX + deltaX)),
              y: Math.max(0, Math.min(img.naturalHeight - area.height, startY + deltaY))
            }
          : area
      ));
    };

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      console.log('✅ Finished dragging area:', areaId);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Check API health and AI detection readiness
  const checkApiHealth = async (baseUrl: string): Promise<boolean> => {
    try {
      const response = await fetch(`${baseUrl}/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });

      if (!response.ok) return false;

      const healthData = await response.json();
      console.log('🏥 Health check response:', healthData);

      // Check if AI detection service is ready
      const aiReady = healthData.services?.ai_detection === 'ready';
      const status = healthData.status;

      console.log(`🤖 AI Detection: ${aiReady ? 'Ready' : 'Not Ready'} (Status: ${status})`);

      return aiReady;
    } catch (error) {
      console.error('❌ Health check failed:', error);
      return false;
    }
  };

  // Check API status on component mount
  useEffect(() => {
    const checkApiStatus = async () => {
      console.log('🔍 Checking API status...');
      setApiStatus('checking');

      console.log('🔍 Testing primary API:', API_BASE_URL);
      const primaryHealthy = await checkApiHealth(API_BASE_URL);

      if (primaryHealthy) {
        setApiStatus('online');
        console.log('✅ Primary API service is online and AI detection is ready');
        return;
      }

      console.log('🔍 Testing fallback API:', FALLBACK_API_URL);
      const fallbackHealthy = await checkApiHealth(FALLBACK_API_URL);

      if (fallbackHealthy) {
        setApiStatus('online');
        console.log('✅ Fallback API service is online and AI detection is ready');
      } else {
        setApiStatus('offline');
        console.error('❌ Both API services are offline or AI detection is not ready');
        console.error('💡 This could mean:');
        console.error('   - The AI model is still loading');
        console.error('   - The GPU service is not running');
        console.error('   - Network connectivity issues');
      }
    };

    checkApiStatus();
  }, []);

  // Enhanced API call with detailed debugging
  const tryApiCall = async (formData: FormData, operation: string): Promise<Response> => {
    const urls = [API_BASE_URL, FALLBACK_API_URL];

    // Debug: Log all FormData parameters
    console.log(`🔍 ${operation} - FormData parameters:`);
    for (let [key, value] of formData.entries()) {
      if (key !== 'image') {
        console.log(`   ${key}: "${value}"`);
      } else {
        console.log(`   ${key}: [Blob ${(value as Blob).size} bytes]`);
      }
    }

    for (let i = 0; i < urls.length; i++) {
      const baseUrl = urls[i]!;
      const url = `${baseUrl}/process_image`;

      console.log(`📤 ${operation} request to:`, url);

      try {
        const response = await fetch(url, {
          method: 'POST',
          body: formData,
          headers: {
            'Accept': 'application/json, image/*',
          }
        });

        console.log(`📊 ${operation} response status:`, response.status);
        console.log(`📊 ${operation} response headers:`, Object.fromEntries(response.headers.entries()));

        if (response.ok) {
          console.log(`✅ ${operation} successful with URL:`, url);
          return response;
        } else {
          console.warn(`⚠️ ${operation} failed with status ${response.status} from:`, url);

          // Try to get error details
          const contentType = response.headers.get('content-type');
          let errorDetails = '';

          if (contentType?.includes('application/json')) {
            try {
              const errorJson = await response.json();
              errorDetails = JSON.stringify(errorJson, null, 2);
              console.error(`❌ ${operation} JSON error response:`, errorJson);
            } catch (e) {
              errorDetails = 'Failed to parse JSON error response';
            }
          } else {
            errorDetails = await response.text();
            console.error(`❌ ${operation} text error response:`, errorDetails);
          }

          if (i === urls.length - 1) {
            throw new Error(`${operation} failed: ${response.status} - ${errorDetails}`);
          }
        }
      } catch (error) {
        console.error(`❌ ${operation} error with URL ${url}:`, error);
        if (i === urls.length - 1) {
          throw new Error(`API service unavailable: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
        console.log(`🔄 Trying fallback URL...`);
      }
    }

    throw new Error(`${operation} failed: All API endpoints unavailable`);
  };

  // Enhanced debug function to test API endpoints
  const testBlurAPI = async () => {
    if (!imagePreview) {
      console.error('❌ No image available for testing');
      return;
    }

    console.log('🧪 Testing blur API endpoints...');
    setIsProcessing(true);

    try {
      // Create a simple test image blob
      let imageBlob: Blob;
      if (imagePreview.startsWith('data:')) {
        const response = await fetch(imagePreview);
        imageBlob = await response.blob();
      } else {
        const imageResponse = await fetch(imagePreview);
        imageBlob = await imageResponse.blob();
      }

      console.log('📷 Test image blob size:', imageBlob.size, 'bytes');

      // Test 1: Minimal blur request (most likely to work)
      console.log('🧪 Test 1: Minimal blur request');
      const minimalFormData = new FormData();
      minimalFormData.append('image', imageBlob, 'image.jpg');
      minimalFormData.append('text_prompt', 'test');
      minimalFormData.append('blur_detections', 'false');
      minimalFormData.append('blur_strength', '50');
      minimalFormData.append('blur_type', 'blur');

      try {
        const minimalResponse = await tryApiCall(minimalFormData, 'Test Minimal Blur');
        const contentType = minimalResponse.headers.get('content-type');
        console.log('✅ Minimal blur test passed, content-type:', contentType);

        if (contentType?.startsWith('image/')) {
          const blob = await minimalResponse.blob();
          console.log('📷 Received image blob size:', blob.size, 'bytes');
          const testUrl = URL.createObjectURL(blob);
          setProcessedImageUrl(testUrl);
          console.log('✅ Test image displayed successfully!');
        }
      } catch (error) {
        console.error('❌ Minimal blur test failed:', error);
      }

      // Test 2: Full screen blur
      console.log('🧪 Test 2: Full screen blur');
      const fullScreenFormData = new FormData();
      fullScreenFormData.append('image', imageBlob, 'image.jpg');
      fullScreenFormData.append('text_prompt', 'full_screen_blur');
      fullScreenFormData.append('blur_detections', 'true');
      fullScreenFormData.append('blur_strength', '50');
      fullScreenFormData.append('blur_type', 'blur');
      fullScreenFormData.append('full_screen', 'true');

      try {
        const fullScreenResponse = await tryApiCall(fullScreenFormData, 'Test Full Screen Blur');
        console.log('✅ Full screen blur test passed');
      } catch (error) {
        console.error('❌ Full screen blur test failed:', error);
      }

    } catch (error) {
      console.error('❌ API test failed:', error);
      setError(`API test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };



  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowUserDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);





  // Load image - use signed URL directly instead of converting to base64
  const loadImageAsBase64 = async () => {
    setIsLoadingImage(true);
    setImageLoadError(null);
    console.log('🖼️ Loading image');
    console.log('📁 Media file info:', mediaFile);
    console.log('📁 Original image URL:', originalImageUrl);

    // Check if we have a valid image URL (not a placeholder)
    if (!originalImageUrl ||
        originalImageUrl.includes('placeholder') ||
        originalImageUrl.includes('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==')) {
      console.log('📷 No valid image URL provided, showing upload interface');
      setImageLoadError('No image provided. Please upload an image to get started.');
      setIsLoadingImage(false);
      return;
    }

    try {
      // If it's already a base64 string, use it directly
      if (originalImageUrl.startsWith('data:')) {
        console.log('✅ Using base64 image directly');
        setImagePreview(originalImageUrl);
        setImageLoadError(null);
        setIsLoadingImage(false);
        return;
      }

      // For Supabase signed URLs, use them directly without conversion
      // The browser can load images from signed URLs without issues
      console.log('✅ Using signed URL directly:', originalImageUrl);
      setImagePreview(originalImageUrl);
      setImageLoadError(null);
      setIsLoadingImage(false);
    } catch (error) {
      console.error('❌ Error loading image:', error);
      setImageLoadError('Failed to load image. Please try uploading again.');
      setIsLoadingImage(false);
    }
  };

  // Load saved processing state
  const loadSavedProcessingState = async () => {
    if (!mediaFile.id) return;

    try {
      console.log('🔍 Loading saved processing state for file:', mediaFile.id);
      const response = await fetch(`/api/studio/load-processing?fileId=${mediaFile.id}`);

      if (response.ok) {
        const result = await response.json();

        if (result.found && result.processing_state) {
          const state = result.processing_state;
          console.log('✅ Found saved processing state:', state);

          // Restore comprehensive processing settings
          if (state.processing_settings) {
            const settings = state.processing_settings;

            // Restore current settings
            if (settings.blur_intensity !== undefined) {
              setBlurIntensity(settings.blur_intensity);
            }
            if (settings.blur_type) {
              setBlurType(settings.blur_type);
            }
            if (settings.confidence_threshold !== undefined) {
              setConfidenceThreshold(settings.confidence_threshold);
            }

            // Restore detection history
            if (settings.all_detected_objects && Array.isArray(settings.all_detected_objects)) {
              setAllDetectedObjects(settings.all_detected_objects);
              console.log('✅ Restored all detected objects:', settings.all_detected_objects.length);
            }
            if (settings.filtered_detected_objects && Array.isArray(settings.filtered_detected_objects)) {
              setDetectedObjects(settings.filtered_detected_objects);
              console.log('✅ Restored filtered objects:', settings.filtered_detected_objects.length);
            }
            if (settings.last_used_prompt) {
              setLastUsedPrompt(settings.last_used_prompt);
            }

            // Legacy compatibility
            if (settings.detected_objects && Array.isArray(settings.detected_objects) && !settings.all_detected_objects) {
              // Old format - treat as both all and filtered
              setAllDetectedObjects(settings.detected_objects);
              setDetectedObjects(settings.detected_objects);
            }
            if (settings.detection_categories) {
              setDetectionCategories(settings.detection_categories);
            }
            if (settings.custom_blur_areas && Array.isArray(settings.custom_blur_areas)) {
              setCustomBlurAreas(settings.custom_blur_areas);
            }

            console.log('🔄 Restored processing settings:', {
              totalObjects: settings.all_detected_objects?.length || 0,
              visibleObjects: settings.filtered_detected_objects?.length || 0,
              uniquePrompts: settings.unique_prompts_used?.length || 0,
              confidenceThreshold: settings.confidence_threshold
            });
          }

          // Restore processed image if available
          if (state.processed_image_url) {
            // If it's a base64 string, use it directly
            // If it's a URL, try to load it
            if (state.processed_image_url.startsWith('data:')) {
              setProcessedImageUrl(state.processed_image_url);
              console.log('✅ Restored processed image from base64');
            } else {
              // Try to load the URL, if it fails, clear it
              try {
                const img = new Image();
                img.onload = () => {
                  setProcessedImageUrl(state.processed_image_url);
                  console.log('✅ Restored processed image from URL');
                };
                img.onerror = () => {
                  console.warn('⚠️ Saved processed image URL is no longer valid');
                  setProcessedImageUrl('');
                };
                img.src = state.processed_image_url;
              } catch (error) {
                console.warn('⚠️ Failed to load saved processed image:', error);
                setProcessedImageUrl('');
              }
            }
          }

          console.log('🎯 Processing state restored successfully');
        } else {
          console.log('ℹ️ No saved processing state found');
        }
      }
    } catch (error) {
      // CRITICAL FIX Issue 5: Silent error handling - no user-facing errors
      console.log('⚠️ Failed to load processing state (silent handling):', error);
      // Continue without showing errors to user - this prevents "File Not Found" messages
    }
  };

  // Load image and saved state on component mount
  useEffect(() => {
    loadImageAsBase64();
    loadSavedProcessingState();
  }, [originalImageUrl, mediaFile]);



  // Initialize with media file
  useEffect(() => {
    console.log('🎬 Initializing GuardiaVision Studio with media file:', mediaFile.original_filename);
    setImagePreview(originalImageUrl);
  }, [mediaFile, originalImageUrl]);

  // Fetch all user images for navigation
  useEffect(() => {
    if (user?.id) {
      fetchAllUserImages();
    }
  }, [user?.id]);



  // Toggle switch component matching the screenshot
  const ToggleSwitch = ({
    enabled,
    onChange,
    label,
    hasWarning = false
  }: {
    enabled: boolean;
    onChange: (enabled: boolean) => void;
    label: string;
    hasWarning?: boolean;
  }) => (
    <div className="flex items-center justify-between py-3">
      <div className="flex items-center gap-2">
        <span className="text-white text-sm font-medium">{label}</span>
        {hasWarning && (
          <div className="w-4 h-4 bg-orange-500 rounded-full flex items-center justify-center">
            <span className="text-white text-xs font-bold">!</span>
          </div>
        )}
      </div>
      <button
        onClick={() => onChange(!enabled)}
        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
          enabled ? 'bg-purple-600' : 'bg-gray-600'
        }`}
      >
        <span
          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
            enabled ? 'translate-x-6' : 'translate-x-1'
          }`}
        />
      </button>
    </div>
  );



  // Step 1: Sequential prompt processing - each prompt as separate API call
  const detectOnly = async () => {
    if (!imagePreview) {
      setError('No image available for processing');
      return;
    }

    const activePrompts = getActivePrompts();
    if (activePrompts.length === 0) {
      setError('Please enter what you want to detect in at least one prompt box');
      return;
    }

    if (apiStatus === 'offline') {
      setError('AI service is currently offline. Please check your connection or try again later.');
      return;
    }

    // 💰 CREDIT SYSTEM: Check credits for ALL prompts (3 credits per prompt)
    const totalCreditsNeeded = activePrompts.length * 3;
    console.log(`💰 Checking credits for ${activePrompts.length} prompts (${totalCreditsNeeded} credits required)...`);
    const hasCredits = await checkAndDeductCredits(totalCreditsNeeded);
    if (!hasCredits) {
      console.log('❌ Detection cancelled due to insufficient credits');
      return; // Error message already set by checkAndDeductCredits
    }

    setIsProcessing(true);
    setError(null);

    try {
      const startTime = performance.now();
      console.log(`🔍 Starting sequential AI detection with ${activePrompts.length} prompts:`, activePrompts);

      // Store the prompts for future use
      setLastUsedPrompt(activePrompts.join(', '));

      // Prepare image blob once for all API calls
      let imageBlob: Blob;
      if (imagePreview.startsWith('data:')) {
        const response = await fetch(imagePreview);
        imageBlob = await response.blob();
        console.log('📷 Fast base64 to blob conversion, size:', imageBlob.size, 'bytes');
      } else {
        const imageResponse = await fetch(imagePreview);
        imageBlob = await imageResponse.blob();
        console.log('📷 Fetched image blob from URL, size:', imageBlob.size, 'bytes');
      }

      // Get the highest existing ID to continue numbering across all prompts
      let maxExistingId = allDetectedObjects.length > 0
        ? Math.max(...allDetectedObjects.map(obj => obj.id))
        : 0;

      let allNewObjects: DetectedObject[] = [];
      let successfulPrompts = 0;
      let failedPrompts = 0;

      // Process each prompt sequentially
      for (let promptIndex = 0; promptIndex < activePrompts.length; promptIndex++) {
        const currentPrompt = activePrompts[promptIndex]?.trim() || '';

        try {
          console.log(`🔍 Processing prompt ${promptIndex + 1}/${activePrompts.length}: "${currentPrompt}"`);

          // Create FormData for this specific prompt
          const formData = new FormData();
          formData.append('image', imageBlob, 'image.jpg');
          formData.append('text_prompt', currentPrompt);
          formData.append('score_threshold', '0.1'); // Low threshold to get all detections
          formData.append('visualize', 'false'); // FALSE = JSON response with bboxes
          formData.append('blur_detections', 'false'); // No blurring
          formData.append('blur_strength', '25'); // Required parameter
          formData.append('blur_type', 'blur'); // Required parameter

          console.log(`📤 API Call ${promptIndex + 1}: Sending prompt "${currentPrompt}"`);

          const response = await tryApiCall(formData, `Detection-Prompt-${promptIndex + 1}`);
          const result = await response.json();

          console.log(`✅ Prompt ${promptIndex + 1} API Response:`, result);
          console.log(`📊 Detected objects count for "${currentPrompt}":`, result.bboxes?.length || 0);

          // Process detection results for this prompt
          if (result.bboxes && result.bboxes.length > 0) {
            const promptObjects = result.bboxes.map((bbox: number[], index: number) => ({
              id: maxExistingId + index + 1,
              label: `${currentPrompt} #${index + 1}`, // Individual prompt labeling
              confidence: Math.round((result.scores?.[index] || 0.8) * 100),
              bbox: bbox as [number, number, number, number],
              color: `hsl(${((maxExistingId + index) * 60) % 360}, 70%, 50%)`,
              selected: true,
              prompt: currentPrompt // Store the specific prompt that detected this object
            }));

            allNewObjects = [...allNewObjects, ...promptObjects];
            maxExistingId += promptObjects.length; // Update ID counter for next prompt
            successfulPrompts++;

            console.log(`🎯 Prompt "${currentPrompt}" found ${promptObjects.length} objects`);
            console.log(`📊 Objects:`, promptObjects.map((obj: DetectedObject) => `${obj.label} (${obj.confidence}%)`));
          } else {
            console.log(`🔍 Prompt "${currentPrompt}" found no objects`);
            successfulPrompts++; // Still count as successful even if no objects found
          }

        } catch (error) {
          console.error(`❌ Prompt "${currentPrompt}" failed:`, error);
          failedPrompts++;
          // Continue processing remaining prompts
        }
      }

      // Accumulate all results
      if (allNewObjects.length > 0) {
        const updatedAllObjects = [...allDetectedObjects, ...allNewObjects];
        setAllDetectedObjects(updatedAllObjects);

        console.log(`🎯 Sequential detection complete:`);
        console.log(`   - Successful prompts: ${successfulPrompts}/${activePrompts.length}`);
        console.log(`   - Failed prompts: ${failedPrompts}/${activePrompts.length}`);
        console.log(`   - Total new objects: ${allNewObjects.length}`);
        console.log(`   - Total objects now: ${updatedAllObjects.length}`);

        // Filter all objects by confidence threshold
        const filteredObjects = updatedAllObjects.filter(obj => obj.confidence >= confidenceThreshold * 100);
        console.log(`🎯 Filtering: ${filteredObjects.length}/${updatedAllObjects.length} objects pass ${confidenceThreshold * 100}% threshold`);
        setDetectedObjects(filteredObjects);

        // Performance monitoring
        const endTime = performance.now();
        console.log(`⚡ Sequential detection completed in ${Math.round(endTime - startTime)}ms`);

      } else {
        console.log('🔍 No objects detected from any prompts');
        if (failedPrompts === activePrompts.length) {
          setError('All detection prompts failed. Please try again.');
        }
      }

      // Show summary to user
      if (failedPrompts > 0) {
        setError(`${failedPrompts} out of ${activePrompts.length} prompts failed. ${successfulPrompts} prompts processed successfully.`);
      }

    } catch (error) {
      console.error('❌ Sequential detection failed:', error);
      setError(`Detection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Simplified blur function for testing new API
  const applySimpleBlur = async () => {
    if (!imagePreview) {
      setError('No image available for processing');
      return;
    }

    console.log('🚀 STARTING SIMPLE BLUR PROCESS');
    setIsProcessing(true);
    setError(null);

    try {
      // Convert image to blob
      let imageBlob: Blob;
      if (imagePreview.startsWith('data:')) {
        const response = await fetch(imagePreview);
        imageBlob = await response.blob();
      } else {
        const imageResponse = await fetch(imagePreview);
        imageBlob = await imageResponse.blob();
      }

      console.log('📷 Image blob size:', imageBlob.size, 'bytes');

      // Create minimal FormData with only essential parameters
      const formData = new FormData();
      formData.append('image', imageBlob, 'image.jpg');
      formData.append('blur_strength', blurIntensity.toString());
      formData.append('blur_type', blurType);

      // Try different parameter combinations
      const parameterSets = [
        // Set 1: Minimal parameters
        {
          text_prompt: 'blur_image',
          blur_detections: 'false'
        },
        // Set 2: Full screen blur
        {
          text_prompt: 'full_screen_blur',
          blur_detections: 'true',
          full_screen: 'true'
        },
        // Set 3: Traditional parameters
        {
          text_prompt: 'test',
          score_threshold: '0.5',
          visualize: 'false',
          blur_detections: 'false'
        }
      ];

      for (let i = 0; i < parameterSets.length; i++) {
        console.log(`🧪 Trying parameter set ${i + 1}:`, parameterSets[i]);

        const testFormData = new FormData();
        testFormData.append('image', imageBlob, 'image.jpg');
        testFormData.append('blur_strength', blurIntensity.toString());
        testFormData.append('blur_type', blurType);

        // Add parameters from current set
        const currentSet = parameterSets[i];
        if (currentSet) {
          Object.entries(currentSet).forEach(([key, value]) => {
            testFormData.append(key, value);
          });
        }

        try {
          const response = await tryApiCall(testFormData, `Simple Blur Set ${i + 1}`);
          const contentType = response.headers.get('content-type');

          if (contentType?.startsWith('image/')) {
            const blob = await response.blob();
            if (blob.size > 0) {
              const imageUrl = URL.createObjectURL(blob);
              setProcessedImageUrl(imageUrl);
              console.log(`✅ Simple blur successful with parameter set ${i + 1}!`);
              return; // Success, exit the loop
            }
          }
        } catch (error) {
          console.error(`❌ Parameter set ${i + 1} failed:`, error);
          if (i === parameterSets.length - 1) {
            throw error; // Re-throw if this was the last attempt
          }
        }
      }

    } catch (error) {
      console.error('❌ Simple blur failed:', error);
      setError(`Simple blur failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Simple blur function that actually works
  const applyBlur = async () => {
    console.log('🚀 STARTING BLUR PROCESS');
    console.log('🔍 Current API URL:', API_BASE_URL);

    if (!imagePreview) {
      console.error('❌ No image available');
      setError('No image available');
      return;
    }

    const visibleDetectedObjects = detectedObjects.filter(obj => obj.selected);
    const visibleCustomAreas = customBlurAreas.filter(area => area.visible);

    if (visibleDetectedObjects.length === 0 && visibleCustomAreas.length === 0) {
      console.error('❌ No visible objects or custom areas');
      setError('No visible objects or custom areas to blur. Please detect objects or create custom areas first.');
      return;
    }

    console.log(`🎯 Will blur ${visibleDetectedObjects.length} detected objects + ${visibleCustomAreas.length} custom areas`);

    // 💰 CREDIT SYSTEM: Check and deduct 2 credits for blur operation
    console.log('💰 Checking credits for blur operation (2 credits required)...');
    const hasCredits = await checkAndDeductCredits(2);
    if (!hasCredits) {
      console.log('❌ Blur operation cancelled due to insufficient credits');
      return; // Error message already set by checkAndDeductCredits
    }

    // Get the last used prompt (simple approach)
    const promptToUse = lastUsedPrompt || getAllPromptsText();

    if (!promptToUse) {
      console.error('❌ No prompt available');
      setError('No detection prompt available');
      return;
    }

    console.log('✅ BLUR STARTING:', {
      prompt: promptToUse,
      intensity: blurIntensity,
      type: blurType,
      objects: detectedObjects.length,
      apiUrl: API_BASE_URL
    });

    console.log('🎯 Blur parameters:', {
      prompt: promptToUse,
      intensity: blurIntensity,
      type: blurType
    });

    setIsProcessing(true);
    setError(null);

    try {
      const startTime = performance.now();
      console.log('� Making API call...');

      const formData = new FormData();

      // Convert image to blob
      const imageResponse = await fetch(imagePreview);
      const imageBlob = await imageResponse.blob();

      formData.append('image', imageBlob, 'image.jpg');
      formData.append('text_prompt', promptToUse);
      formData.append('score_threshold', confidenceThreshold.toString()); // Use user's confidence setting
      formData.append('visualize', 'false');
      formData.append('blur_detections', 'true');
      formData.append('blur_strength', blurIntensity.toString());
      formData.append('blur_type', blurType);

      // Add custom blur areas if any exist
      if (visibleCustomAreas.length > 0) {
        const customAreasData = visibleCustomAreas.map(area => ({
          x: Math.round(area.x),
          y: Math.round(area.y),
          width: Math.round(area.width),
          height: Math.round(area.height)
        }));
        formData.append('custom_blur_areas', JSON.stringify(customAreasData));
        console.log('📦 Added custom blur areas to applyBlur request:', customAreasData);
      }

      console.log('� API Parameters:', {
        prompt: promptToUse,
        strength: blurIntensity,
        type: blurType
      });

      // Debug: Log all form data
      console.log('📋 Complete FormData being sent:');
      for (let [key, value] of formData.entries()) {
        if (key === 'image') {
          console.log(`   ${key}: [Blob ${(value as Blob).size} bytes]`);
        } else {
          console.log(`   ${key}: "${value}"`);
        }
      }

      console.log('📤 Calling API with URL:', `${API_BASE_URL}/process_image`);

      const response = await tryApiCall(formData, 'Blur');

      // The API returns a FileResponse (image file)
      const contentType = response.headers.get('content-type');
      console.log('✅ API Response received');
      console.log('📄 Response content type:', contentType);
      console.log('📋 Response headers:', Object.fromEntries(response.headers.entries()));

      // The response should be an image file when blur_detections=true
      const responseBlob = await response.blob();
      console.log('Response blob size:', responseBlob.size, 'bytes');
      console.log('Response blob type:', responseBlob.type);

      if (responseBlob.size > 0) {
        const imageUrl = URL.createObjectURL(responseBlob);
        console.log('✅ Blur applied successfully! Image URL:', imageUrl);
        console.log('🔄 Setting processedImageUrl state...');
        setProcessedImageUrl(imageUrl);

        // Performance monitoring
        const endTime = performance.now();
        console.log(`⚡ Blur completed in ${Math.round(endTime - startTime)}ms`);

        // Log immediately and auto-save (non-blocking)
        console.log('🖼️ Current processedImageUrl state:', imageUrl);
        console.log('🖼️ Image element src should be:', imageUrl);

        // Auto-save handled by useEffect
      } else {
        throw new Error('Received empty response from blur API');
      }

    } catch (error) {
      console.error('❌ Blur application failed:', error);
      setError(`Blur application failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // UNIFIED BLUR FUNCTION - Handles both detected objects AND custom areas
  const blurAllDetectedObjects = async () => {
    console.log('🎯 UNIFIED BLUR: Blurring ALL content (detected objects + custom areas)');

    if (!imagePreview) {
      setError('No image available for processing');
      return;
    }

    const visibleDetectedObjects = detectedObjects.filter(obj => obj.selected);
    const visibleCustomAreas = customBlurAreas.filter(area => area.visible);

    if (visibleDetectedObjects.length === 0 && visibleCustomAreas.length === 0) {
      setError('No visible objects or custom areas to blur. Please detect objects or create custom areas first.');
      return;
    }

    console.log(`🎯 UNIFIED BLUR: ${visibleDetectedObjects.length} detected objects + ${visibleCustomAreas.length} custom areas`);

    // 💰 CREDIT SYSTEM: Check and deduct 2 credits for blur operation
    console.log('💰 Checking credits for blur operation (2 credits required)...');
    const hasCredits = await checkAndDeductCredits(2);
    if (!hasCredits) {
      console.log('❌ Blur operation cancelled due to insufficient credits');
      return; // Error message already set by checkAndDeductCredits
    }

    // Handle prompts for different scenarios
    let promptToUse = '';

    if (visibleDetectedObjects.length > 0) {
      // We have detected objects - use their prompts
      const uniquePrompts = [...new Set(visibleDetectedObjects.map(obj => obj.prompt).filter(Boolean))];
      promptToUse = uniquePrompts.join(' ') || lastUsedPrompt || getAllPromptsText();
      console.log('🎯 Using prompts from detected objects:', uniquePrompts);
    } else if (visibleCustomAreas.length > 0) {
      // Only custom areas - use a generic prompt or last used prompt
      promptToUse = lastUsedPrompt || getAllPromptsText() || 'custom_blur_areas';
      console.log('🎯 Using prompt for custom areas only:', promptToUse);
    }

    if (!promptToUse) {
      // Fallback for custom areas when no prompt is available
      promptToUse = 'custom_blur_areas';
      console.log('🎯 Using fallback prompt for custom areas:', promptToUse);
    }

    console.log('🎯 UNIFIED BLUR - Final prompt:', promptToUse);
    console.log('🎯 UNIFIED BLUR - Content to blur:', {
      detectedObjects: visibleDetectedObjects.length,
      customAreas: visibleCustomAreas.length
    });

    setIsProcessing(true);
    setError(null);

    try {
      const formData = new FormData();

      // Convert image to blob with error handling
      let imageBlob: Blob;
      if (imagePreview.startsWith('data:')) {
        // Use fetch for base64 conversion
        const response = await fetch(imagePreview);
        imageBlob = await response.blob();
        console.log('📷 Converted base64 to blob for blur, size:', imageBlob.size, 'bytes');
      } else {
        const imageResponse = await fetch(imagePreview);
        imageBlob = await imageResponse.blob();
        console.log('📷 Fetched image blob from URL for blur, size:', imageBlob.size, 'bytes');
      }

      if (imageBlob.size === 0) {
        throw new Error('Image blob is empty - cannot process');
      }

      formData.append('image', imageBlob, 'image.jpg');
      formData.append('text_prompt', promptToUse);
      formData.append('score_threshold', confidenceThreshold.toString());
      formData.append('visualize', 'false');

      // ✅ IMPORTANT: Only set blur_detections=true if we have detected objects
      // For custom areas only, we don't want AI detection to run
      if (visibleDetectedObjects.length > 0) {
        formData.append('blur_detections', 'true');
        console.log('🤖 AI detection enabled - will blur detected objects');
      } else {
        formData.append('blur_detections', 'false');
        console.log('🎯 AI detection disabled - custom areas only');
      }

      formData.append('blur_strength', blurIntensity.toString());
      formData.append('blur_type', blurType);

      // Add custom blur areas if any exist
      if (visibleCustomAreas.length > 0) {
        const customAreasData = visibleCustomAreas.map(area => ({
          x: Math.round(area.x),
          y: Math.round(area.y),
          width: Math.round(area.width),
          height: Math.round(area.height)
        }));
        formData.append('custom_blur_areas', JSON.stringify(customAreasData));
        console.log('📦 UNIFIED BLUR: Added custom blur areas:', customAreasData);

        // If we ONLY have custom areas (no detected objects), set special flag
        if (visibleDetectedObjects.length === 0) {
          formData.append('custom_areas_only', 'true');
          console.log('🎯 UNIFIED BLUR: Custom areas ONLY mode (no detected objects)');
        }
      }

      console.log('📤 UNIFIED BLUR: Sending request with:', {
        prompt: promptToUse,
        detectedObjects: visibleDetectedObjects.length,
        customAreas: visibleCustomAreas.length,
        customAreasOnly: visibleDetectedObjects.length === 0 && visibleCustomAreas.length > 0
      });

      const response = await tryApiCall(formData, 'Blur All Objects');

      // Handle response
      const contentType = response.headers.get('content-type');
      console.log('✅ Blur all objects response received');

      if (contentType && contentType.startsWith('image/')) {
        const blob = await response.blob();
        console.log('📷 Received blurred image, size:', blob.size, 'bytes');

        if (blob.size > 0) {
          const imageUrl = URL.createObjectURL(blob);
          setProcessedImageUrl(imageUrl);
          console.log('✅ All objects blurred successfully!');
        } else {
          throw new Error('Received empty image blob');
        }
      } else {
        const text = await response.text();
        console.error('❌ Unexpected response format:', text);
        throw new Error(`Unexpected response format: ${contentType}`);
      }

    } catch (error) {
      console.error('❌ Failed to blur all objects:', error);
      setError(`Failed to blur all objects: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };

  
      

    // Apply full screen blur using the correct dedicated endpoint
  const applyFullScreenBlur = async () => {
    if (!imagePreview) {
      setError('No image available for processing');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      console.log('🌀 Applying full screen blur using dedicated endpoint...');
      console.log('📊 Full screen blur parameters:', {
        blurIntensity,
        blurType
      });

      const formData = new FormData();

      // Convert image to blob using the same method as other functions
      let imageBlob: Blob;
      if (imagePreview.startsWith('data:')) {
        const response = await fetch(imagePreview);
        imageBlob = await response.blob();
        console.log('📷 Fast base64 to blob conversion for full screen blur, size:', imageBlob.size, 'bytes');
      } else {
        const imageResponse = await fetch(imagePreview);
        imageBlob = await imageResponse.blob();
        console.log('📷 Fetched image blob from URL for full screen blur, size:', imageBlob.size, 'bytes');
      }
      
      formData.append('image', imageBlob, 'image.jpg');
      formData.append('blur_strength', blurIntensity.toString());
      formData.append('blur_type', blurType);

      console.log('📤 Sending full screen blur request to:', `${API_BASE_URL}/full_screen_blur`);

      // Call the dedicated full screen blur endpoint
      const response = await tryApiCallFullScreen(formData, 'Full Screen Blur');

      // Handle the response
      const contentType = response.headers.get('content-type');
      console.log('✅ Full screen blur API Response received');
      console.log('📄 Response content type:', contentType);

      if (contentType && contentType.startsWith('image/')) {
        const blob = await response.blob();
        console.log('📷 Received image blob, size:', blob.size, 'bytes');

        if (blob.size > 0) {
          const imageUrl = URL.createObjectURL(blob);
          setProcessedImageUrl(imageUrl);
          console.log('✅ Full screen blur applied successfully! Image URL:', imageUrl);
        } else {
          throw new Error('Received empty image blob');
        }
      } else {
        const text = await response.text();
        console.error('❌ Unexpected response format:', text);
        throw new Error(`Unexpected response format: ${contentType}`);
      }

    } catch (error) {
      console.error('❌ Full screen blur failed:', error);
      setError(`Full screen blur failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // New helper function for full screen blur API calls
  const tryApiCallFullScreen = async (formData: FormData, operation: string): Promise<Response> => {
    const urls = [API_BASE_URL, FALLBACK_API_URL];

    // Debug: Log all FormData parameters
    console.log(`🔍 ${operation} - FormData parameters:`);
    for (let [key, value] of formData.entries()) {
      if (key !== 'image') {
        console.log(`   ${key}: "${value}"`);
      } else {
        console.log(`   ${key}: [Blob ${(value as Blob).size} bytes]`);
      }
    }

    for (let i = 0; i < urls.length; i++) {
      const baseUrl = urls[i]!;
      const url = `${baseUrl}/full_screen_blur`; // Use the correct endpoint

      console.log(`📤 ${operation} request to:`, url);

      try {
        const response = await fetch(url, {
          method: 'POST',
          body: formData,
          headers: {
            'Accept': 'application/json, image/*',
          }
        });

        console.log(`📊 ${operation} response status:`, response.status);
        console.log(`📊 ${operation} response headers:`, Object.fromEntries(response.headers.entries()));

        if (response.ok) {
          console.log(`✅ ${operation} successful with URL:`, url);
          return response;
        } else {
          console.warn(`⚠️ ${operation} failed with status ${response.status} from:`, url);

          // Try to get error details
          const contentType = response.headers.get('content-type');
          let errorDetails = '';

          if (contentType?.includes('application/json')) {
            try {
              const errorJson = await response.json();
              errorDetails = JSON.stringify(errorJson, null, 2);
              console.error(`❌ ${operation} JSON error response:`, errorJson);
            } catch (e) {
              errorDetails = 'Failed to parse JSON error response';
            }
          } else {
            errorDetails = await response.text();
            console.error(`❌ ${operation} text error response:`, errorDetails);
          }

          if (i === urls.length - 1) {
            throw new Error(`${operation} failed: ${response.status} - ${errorDetails}`);
          }
        }
      } catch (error) {
        console.error(`❌ ${operation} error with URL ${url}:`, error);
        if (i === urls.length - 1) {
          throw new Error(`API service unavailable: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
        console.log(`🔄 Trying fallback URL...`);
      }
    }

    throw new Error(`${operation} failed: All API endpoints unavailable`);
  };



  // Handle click on image (disabled custom blur for now)
  const handleImageClick = (event: React.MouseEvent<HTMLImageElement>) => {
    // Custom blur areas not supported by current API
    console.log('Image clicked at:', event.clientX, event.clientY);
  };

  // Handle custom area resizing - FIXED VERSION
  const handleCustomAreaResize = (areaId: string, handle: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    console.log(`🔧 Starting resize for area ${areaId} from handle ${handle}`);

    const img = imageRef.current;
    if (!img) {
      console.error('❌ No image element found for resize');
      return;
    }

    const startMouseX = e.clientX;
    const startMouseY = e.clientY;

    // Find the area being resized
    const startArea = customBlurAreas.find(area => area.id === areaId);
    if (!startArea) return;

    // Get image display dimensions and natural dimensions for proper scaling
    const imgRect = img.getBoundingClientRect();
    const scaleX = img.naturalWidth / imgRect.width;
    const scaleY = img.naturalHeight / imgRect.height;

    console.log(`🔧 Image scaling: scaleX=${scaleX.toFixed(2)}, scaleY=${scaleY.toFixed(2)}`);
    console.log(`🔧 Start area:`, startArea);

    const handleMouseMove = (moveEvent: MouseEvent) => {
      // Calculate mouse movement in display pixels, then convert to natural image pixels
      const displayDeltaX = moveEvent.clientX - startMouseX;
      const displayDeltaY = moveEvent.clientY - startMouseY;
      const deltaX = displayDeltaX * scaleX;
      const deltaY = displayDeltaY * scaleY;

      // Start with the original area
      let newArea = { ...startArea };

      // Update area based on handle being dragged
      switch (handle) {
        // Corner handles
        case 'se': // Bottom-right corner
          newArea.width = Math.max(20, startArea.width + deltaX);
          newArea.height = Math.max(20, startArea.height + deltaY);
          break;
        case 'sw': // Bottom-left corner
          newArea.x = startArea.x + deltaX;
          newArea.width = Math.max(20, startArea.width - deltaX);
          newArea.height = Math.max(20, startArea.height + deltaY);
          break;
        case 'ne': // Top-right corner
          newArea.y = startArea.y + deltaY;
          newArea.width = Math.max(20, startArea.width + deltaX);
          newArea.height = Math.max(20, startArea.height - deltaY);
          break;
        case 'nw': // Top-left corner
          newArea.x = startArea.x + deltaX;
          newArea.y = startArea.y + deltaY;
          newArea.width = Math.max(20, startArea.width - deltaX);
          newArea.height = Math.max(20, startArea.height - deltaY);
          break;

        // Edge handles
        case 'n': // Top edge
          newArea.y = startArea.y + deltaY;
          newArea.height = Math.max(20, startArea.height - deltaY);
          break;
        case 's': // Bottom edge
          newArea.height = Math.max(20, startArea.height + deltaY);
          break;
        case 'w': // Left edge
          newArea.x = startArea.x + deltaX;
          newArea.width = Math.max(20, startArea.width - deltaX);
          break;
        case 'e': // Right edge
          newArea.width = Math.max(20, startArea.width + deltaX);
          break;
      }

      // Ensure area stays within natural image bounds
      newArea.x = Math.max(0, Math.min(newArea.x, img.naturalWidth - 20));
      newArea.y = Math.max(0, Math.min(newArea.y, img.naturalHeight - 20));
      newArea.width = Math.max(20, Math.min(newArea.width, img.naturalWidth - newArea.x));
      newArea.height = Math.max(20, Math.min(newArea.height, img.naturalHeight - newArea.y));

      // Update the area (preserve visibility)
      setCustomBlurAreas(prev =>
        prev.map(area => area.id === areaId ? { ...newArea, visible: area.visible || true } : area)
      );
    };

    const handleMouseUp = () => {
      console.log(`🔧 Finished resizing area ${areaId}`);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      // Auto-save handled by useEffect
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };







  return (
    <div className="min-h-screen bg-[#0a192f] text-white">
      {/* Header Bar - Mobile Responsive */}
      <div className="bg-[#0a192f] border-b border-[#1e3a6f] px-3 sm:px-6 py-3 sm:py-4">
        <div className="relative flex items-center">
          {/* Left Side - Back Button */}
          <button
            onClick={onBack}
            className="flex items-center space-x-1 sm:space-x-2 text-[#94a3b8] hover:text-white transition-colors px-2 sm:px-3 py-2 rounded-lg hover:bg-[#1e3a6f]"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="text-xs sm:text-sm hidden sm:inline">Back to Dashboard</span>
            <span className="text-xs sm:hidden">Back</span>
          </button>

          {/* Center - Logo and Title - Mobile Responsive */}
          <div className="absolute left-1/2 transform -translate-x-1/2 flex items-center space-x-2 sm:space-x-3 cursor-pointer" onClick={() => window.location.href = '/dashboard'}>
            <img
              src="/photo-de-profil-linkedin.png"
              alt="Guardiavision Logo"
              className="h-8 sm:h-10 w-auto"
            />
            <h1 className="text-sm sm:text-xl font-semibold text-white">
              <span className="hidden sm:inline">Guardia<span className="text-[#22C55E]">Vision</span> Studio</span>
              <span className="sm:hidden">Studio</span>
            </h1>
          </div>

          {/* Right Side - User Dropdown */}
          <div className="ml-auto relative" ref={dropdownRef}>
            <button
              onClick={() => setShowUserDropdown(!showUserDropdown)}
              className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm hover:bg-purple-700 transition-colors"
            >
              {getUserInitials()}
            </button>

            {/* Dropdown Menu */}
            {showUserDropdown && (
              <div className="absolute right-0 mt-2 w-48 bg-[#0f1629] border border-[#1e3a6f] rounded-lg shadow-lg z-50">
                <div className="py-1">
                  <button
                    onClick={handleWorkspaceClick}
                    className="flex items-center w-full px-4 py-2 text-sm text-[#94a3b8] hover:text-white hover:bg-[#1e3a6f] transition-colors"
                  >
                    <LayoutGrid className="h-4 w-4 mr-3" />
                    Workspace
                  </button>
                  <button
                    onClick={handleAccountSettings}
                    className="flex items-center w-full px-4 py-2 text-sm text-[#94a3b8] hover:text-white hover:bg-[#1e3a6f] transition-colors"
                  >
                    <Settings className="h-4 w-4 mr-3" />
                    Account Settings
                  </button>
                  <div className="border-t border-[#1e3a6f] my-1"></div>
                  <button
                    onClick={handleSignOut}
                    className="flex items-center w-full px-4 py-2 text-sm text-red-400 hover:text-red-300 hover:bg-[#1e3a6f] transition-colors"
                  >
                    <LogOut className="h-4 w-4 mr-3" />
                    Sign Out
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile/Desktop Layout - Responsive */}
      <div className="flex flex-col lg:flex-row h-[calc(100vh-77px)] sm:h-[calc(100vh-89px)]">
        {/* Left Sidebar - Control Panel - Mobile Responsive */}
        <div className="w-full lg:w-80 bg-[#0f1629] border-b lg:border-b-0 lg:border-r border-[#1e3a6f] flex flex-col max-h-[40vh] lg:max-h-none overflow-y-auto lg:overflow-visible">
          <div className="flex-1 overflow-y-auto">
            {/* Multiple Detection Prompts - Mobile Responsive */}
            <div className="p-2 lg:p-3 border-b border-[#1e3a6f] relative">
              <div className="mb-1 lg:mb-2 flex items-center justify-between">
                <label className="text-white text-xs font-medium flex items-center">
                  <span className="text-purple-400 mr-1 text-xs">●</span>
                  <span className="hidden sm:inline">Detection Prompts</span>
                  <span className="sm:hidden">Prompts</span>
                  <span className="text-xs text-purple-400 ml-1 font-normal hidden lg:inline">Start here</span>
                </label>
                {textPrompts.length < 5 && (
                  <button
                    onClick={addNewPrompt}
                    className="text-xs text-purple-400 hover:text-purple-300 transition-colors flex items-center"
                  >
                    <span className="mr-1">+</span>Add Prompt
                  </button>
                )}
              </div>

              {/* Multiple Prompt Inputs */}
              <div className="space-y-2">
                {textPrompts.map((prompt, index) => (
                  <div key={index} className="relative">
                    {/* Laser border animation - continuous traveling light */}
                    <div className="prompt-laser-border"></div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={prompt}
                        onChange={(e) => updatePrompt(index, e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && canDetect()) {
                            detectOnly();
                          }
                        }}
                        placeholder={`Enter what to detect ${index + 1} (e.g., person, face, car)...`}
                        className={`relative flex-1 px-3 py-2 rounded-lg text-white placeholder-gray-400 focus:outline-none text-sm transition-all duration-300 ${
                          hasUserTyped
                            ? 'bg-[#1e3a6f]/30 border border-[#1e3a6f] focus:border-purple-500'
                            : 'bg-[#1e3a6f]/30 border-2 border-transparent focus:border-blue-400/50 z-10'
                        }`}
                      />
                      {textPrompts.length > 1 && (
                        <button
                          onClick={() => removePrompt(index)}
                          className="text-red-400 hover:text-red-300 transition-colors p-1"
                          title="Remove prompt"
                        >
                          ×
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              <p className={`text-xs mt-2 flex items-center transition-all duration-300 ${
                hasUserTyped ? 'text-gray-400' : 'text-blue-400'
              }`}>
                <span className="mr-1">⚡</span>
                Press Enter to detect objects from all prompts
              </p>
            </div>

            {/* Detection Controls - Compressed */}
            <div className="p-3 border-b border-[#1e3a6f]">
              <div className="space-y-2">
                <ToggleSwitch
                  enabled={detectionCategories.fullScreenBlur}
                  onChange={(enabled) => {
                    setDetectionCategories(prev => ({ ...prev, fullScreenBlur: enabled }));
                    if (enabled && imagePreview) {
                      applyFullScreenBlur();
                    } else if (!enabled) {
                      // Reset to original image when disabled
                      setProcessedImageUrl('');
                    }
                  }}
                  label="Full screen blur"
                  hasWarning={true}
                />
              </div>
            </div>

            {/* Custom Blur Areas - Compressed */}
            <div className="p-2 border-b border-[#1e3a6f]">
              <div className="space-y-2">
                <button
                  onClick={addCustomBlurArea}
                  disabled={!imagePreview}
                  className="w-full flex items-center justify-center space-x-1 px-2 py-1.5 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white rounded text-xs font-medium transition-colors"
                >
                  <Plus className="h-3 w-3" />
                  <span>Add Custom Area</span>
                </button>
              </div>
            </div>

            {/* Bluriness Intensity - Compressed */}
            <div className="p-2 border-b border-[#1e3a6f]">
              <div className="mb-2">
                <label className="text-white text-xs font-medium">
                  Bluriness/Pixilation Intensity
                </label>
              </div>
              <div className="space-y-2">
                <input
                  type="range"
                  min="3"
                  max="150"
                  value={pendingBlurIntensity}
                  onChange={(e) => handleIntensityChange(Number(e.target.value))}
                  className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                  style={{
                    background: `linear-gradient(to right, #8B5CF6 0%, #8B5CF6 ${(pendingBlurIntensity / 150) * 100}%, #374151 ${(pendingBlurIntensity / 150) * 100}%, #374151 100%)`
                  }}
                />
                <div className="flex justify-between text-xs text-gray-400">
                  <span>Subtle</span>
                  <span>Intense</span>
                </div>
              </div>
              <p className="text-gray-400 text-xs mt-2">
                Controls intensity for both blur and pixelation effects. Higher values = more visible effect.
              </p>
            </div>

            {/* Confidence Threshold - Compressed */}
            <div className="p-2 border-b border-[#1e3a6f]">
              <div className="mb-2">
                <label className="text-white text-xs font-medium">
                  Detection Confidence
                  <span className="text-xs text-gray-400 ml-1">({Math.round(confidenceThreshold * 100)}%)</span>
                </label>
              </div>
              <div className="space-y-2">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={confidenceThreshold}
                  onChange={(e) => updateConfidenceThreshold(Number(e.target.value))}
                  className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                  style={{
                    background: `linear-gradient(to right, #10B981 0%, #10B981 ${confidenceThreshold * 100}%, #374151 ${confidenceThreshold * 100}%, #374151 100%)`
                  }}
                />
                <div className="flex justify-between text-xs text-gray-400">
                  <span>Low (0%)</span>
                  <span>High (100%)</span>
                </div>
                <p className="text-xs text-gray-400">
                  Higher values show only more confident detections. Filtered: {allDetectedObjects.length - detectedObjects.length} objects
                </p>
              </div>
            </div>

            {/* Blur effect - Compressed */}
            <div className="p-2 border-b border-[#1e3a6f]">
              <div className="mb-2">
                <label className="text-white text-xs font-medium">Blur/pixilate effect</label>
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => {
                    console.log('🔄 Switching to BLUR mode with immediate effect');
                    handleBlurTypeToggle('blur');
                  }}
                  disabled={!hasBlurrableObjects() || isProcessing}
                  className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    !hasBlurrableObjects() || isProcessing
                      ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                      : blurType === 'blur'
                      ? 'bg-purple-600 text-white hover:bg-purple-700'
                      : 'bg-[#1e3a6f]/30 text-gray-300 hover:bg-[#1e3a6f]'
                  }`}
                >
                  {isProcessing ? 'Processing...' : 'Blur'}
                </button>
                <button
                  onClick={() => {
                    console.log('🔄 Switching to PIXELATE mode with immediate effect');
                    handleBlurTypeToggle('pixelate');
                  }}
                  disabled={!hasBlurrableObjects() || isProcessing}
                  className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    !hasBlurrableObjects() || isProcessing
                      ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                      : blurType === 'pixelate'
                      ? 'bg-purple-600 text-white hover:bg-purple-700'
                      : 'bg-[#1e3a6f]/30 text-gray-300 hover:bg-[#1e3a6f]'
                  }`}
                >
                  {isProcessing ? 'Processing...' : 'Pixelate'}
                </button>
              </div>
            </div>



            {/* API Status - Compressed */}
            <div className="p-2 border-b border-[#1e3a6f]">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${
                    apiStatus === 'online' ? 'bg-green-500' :
                    apiStatus === 'offline' ? 'bg-red-500' :
                    'bg-yellow-500 animate-pulse'
                  }`} />
                  <span className="text-xs text-gray-400">
                    AI Service: {
                      apiStatus === 'online' ? 'Online' :
                      apiStatus === 'offline' ? 'Offline' :
                      'Checking...'
                    }
                  </span>
                </div>
                {apiStatus === 'offline' && (
                  <button
                    onClick={() => window.location.reload()}
                    className="text-xs text-blue-400 hover:text-blue-300 transition-colors"
                    title="Refresh to check API status"
                  >
                    Refresh
                  </button>
                )}
              </div>
            </div>





            {/* Error Display */}
            {error && (
              <div className="p-4">
                <div className="p-3 bg-red-900/20 border border-red-500/30 rounded-lg">
                  <p className="text-red-400 text-xs mb-2">{error}</p>
                  {/* Show upgrade button for credit-related errors */}
                  {error.includes('Insufficient credits') && (
                    <div className="mt-3 flex gap-2">
                      <button
                        onClick={() => window.open('/dashboard/billing', '_blank')}
                        className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-lg transition-colors"
                      >
                        Upgrade Plan
                      </button>
                      <button
                        onClick={() => window.open('/dashboard', '_blank')}
                        className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-xs rounded-lg transition-colors"
                      >
                        View Dashboard
                      </button>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Main Content Area - Mobile Responsive */}
        <div className="flex-1 bg-[#0a192f] flex flex-col min-h-[60vh] lg:min-h-0">
          {/* Loading State */}
          {isLoadingImage && (
            <div className="flex-1 flex items-center justify-center bg-[#0f1629] p-4">
              <div className="text-center text-gray-300">
                <div className="text-6xl mb-4 animate-pulse">🖼️</div>
                <h3 className="text-xl font-semibold mb-2">Loading Image...</h3>
                <p className="text-gray-400">Validating image URL and accessibility</p>
              </div>
            </div>
          )}

          {/* Error Display / Upload Interface */}
          {!isLoadingImage && imageLoadError && (
            <div className="flex-1 flex items-center justify-center bg-[#0f1629] p-4">
              <div className="text-center text-gray-300">
                <div className="text-6xl mb-4">🖼️</div>
                <h3 className="text-xl font-semibold mb-2">Upload an Image</h3>
                <p className="text-gray-400 mb-6">Get started by uploading an image to process</p>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />

                <div className="space-y-4">
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="px-8 py-4 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors font-medium text-lg"
                  >
                    📁 Choose Image File
                  </button>

                  <p className="text-sm text-gray-400">
                    Supports JPG, PNG, GIF, WebP files
                  </p>

                  {originalImageUrl && !originalImageUrl.includes('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==') && (
                    <div className="mt-6 pt-4 border-t border-gray-600">
                      <p className="text-xs text-gray-500 mb-2">Or try loading the original image:</p>
                      <button
                        onClick={loadImageAsBase64}
                        className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors text-sm"
                      >
                        🔄 Load Original Image
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Image Display - Mobile Responsive */}
          {!isLoadingImage && imagePreview && !imageLoadError && (
            <div className="flex-1 bg-[#0f1629] relative">
              {/* Smart Scaling Container - Mobile Responsive */}
              <div className="absolute inset-2 lg:inset-4 overflow-auto rounded-lg bg-[#0a192f]/50 border border-[#1e3a6f]/30 studio-image-container">
                {/* Centered Image Wrapper with Smart Scaling */}
                <div className="w-full h-full flex items-center justify-center p-4">
                  <div className="relative inline-block">
                    {/* Processing Overlay */}
                    {isProcessing && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-20 rounded-lg">
                        <div className="bg-white bg-opacity-90 px-6 py-4 rounded-lg shadow-lg flex items-center space-x-3">
                          <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
                          <span className="text-gray-800 font-medium">Processing...</span>
                        </div>
                      </div>
                    )}

                    <img
                      ref={imageRef}
                      src={processedImageUrl || imagePreview}
                      alt={mediaFile.original_filename}
                      className="block rounded-lg shadow-2xl cursor-default"
                      style={{
                        ...(imageZoomMode === 'fit' ? {
                          // Mobile responsive sizing
                          maxWidth: window.innerWidth < 1024 ? 'calc(100vw - 32px)' : 'calc(100vw - 680px)', // Account for sidebars on desktop
                          maxHeight: window.innerWidth < 1024 ? 'calc(60vh - 100px)' : 'calc(100vh - 200px)', // Account for header + padding
                          objectFit: 'contain', // Maintain aspect ratio while fitting container
                          width: 'auto',
                          height: 'auto'
                        } : {
                          maxWidth: 'none',
                          maxHeight: 'none',
                          width: 'auto',
                          height: 'auto'
                        }),
                        minWidth: window.innerWidth < 1024 ? '200px' : '300px',
                        minHeight: window.innerWidth < 1024 ? '150px' : '200px'
                      }}
                  onClick={handleImageClick}
                  onLoad={() => {
                    console.log('🖼️ Image loaded successfully. Current src:', processedImageUrl || imagePreview);
                    console.log('🖼️ processedImageUrl:', processedImageUrl);
                    console.log('🖼️ imagePreview:', imagePreview);
                  }}
                  onError={(e) => {
                    console.error('❌ Failed to load image. Current src:', e.currentTarget.src);
                    console.error('❌ processedImageUrl:', processedImageUrl);
                    console.error('❌ imagePreview:', imagePreview);

                    // If processed image fails to load, fallback to original
                    if (processedImageUrl && e.currentTarget.src === processedImageUrl) {
                      console.log('🔄 Processed image failed, falling back to original');
                      setProcessedImageUrl('');
                      e.currentTarget.src = imagePreview;
                    } else {
                      console.error('❌ Original image also failed to load');
                      setError('Image failed to display. Please try reloading or upload a new image.');
                    }
                  }}
                />

                {/* Bounding Box Overlay */}
                {(detectedObjects.length > 0 || customBlurAreas.length > 0) && imageRef.current && (
                  <div className="absolute inset-0 pointer-events-none">
                    {detectedObjects.map((detection) => {
                      if (!detection.bbox || !detection.selected) return null;

                      const img = imageRef.current;
                      if (!img) return null;

                      // Get image display dimensions
                      const imgRect = img.getBoundingClientRect();
                      const containerRect = img.parentElement?.getBoundingClientRect();

                      if (!containerRect) return null;

                      // Calculate scale factors
                      const scaleX = imgRect.width / img.naturalWidth;
                      const scaleY = imgRect.height / img.naturalHeight;

                      // Convert bbox coordinates to display coordinates
                      // bbox format: [x1, y1, x2, y2] (top-left and bottom-right corners)
                      const [x1, y1, x2, y2] = detection.bbox;
                      const displayX = x1 * scaleX;
                      const displayY = y1 * scaleY;
                      const displayWidth = (x2 - x1) * scaleX;
                      const displayHeight = (y2 - y1) * scaleY;

                      return (
                        <div
                          key={detection.id}
                          className="absolute border-2 rounded-lg pointer-events-none"
                          style={{
                            left: `${displayX}px`,
                            top: `${displayY}px`,
                            width: `${displayWidth}px`,
                            height: `${displayHeight}px`,
                            borderColor: detection.color,
                            backgroundColor: 'transparent', // No fill color
                            boxShadow: `0 0 0 1px rgba(0,0,0,0.5)`, // Dark outline for visibility
                          }}
                        >
                          {/* Label */}
                          <div
                            className="absolute -top-7 left-0 px-2 py-1 text-xs font-bold text-white rounded shadow-lg"
                            style={{
                              backgroundColor: detection.color,
                              fontSize: '11px',
                              lineHeight: '14px',
                              minWidth: '40px',
                              textAlign: 'center'
                            }}
                          >
                            {detection.label}
                          </div>
                        </div>
                      );
                    })}

                    {/* Custom blur areas */}
                    {customBlurAreas.filter(area => area.visible).map((area) => {
                      const img = imageRef.current;
                      if (!img) return null;

                      const imgRect = img.getBoundingClientRect();

                      // Calculate scale factors
                      const scaleX = imgRect.width / img.naturalWidth;
                      const scaleY = imgRect.height / img.naturalHeight;

                      // Convert area coordinates to display coordinates
                      const displayX = area.x * scaleX;
                      const displayY = area.y * scaleY;
                      const displayWidth = area.width * scaleX;
                      const displayHeight = area.height * scaleY;

                      return (
                        <div
                          key={area.id}
                          className="absolute border-2 border-dashed rounded-lg pointer-events-auto cursor-move group hover:border-purple-400 select-none"
                          style={{
                            left: `${displayX}px`,
                            top: `${displayY}px`,
                            width: `${displayWidth}px`,
                            height: `${displayHeight}px`,
                            borderColor: '#8B5CF6',
                            backgroundColor: 'rgba(139, 92, 246, 0.1)',
                            minWidth: '20px',
                            minHeight: '20px',
                          }}
                          onMouseDown={(e) => {
                            e.stopPropagation();
                            console.log('🖱️ Starting drag for area:', area.id);
                            handleCustomAreaDrag(area.id, area.x, area.y, e);
                          }}
                        >
                          {/* Label with 4-digit ID */}
                          <div className="absolute -top-8 left-0 px-2 py-1 text-xs font-semibold text-white bg-purple-600 rounded shadow-lg z-10">
                            #{area.id}
                          </div>

                          {/* Delete button - Always visible with better styling */}
                          <button
                            className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-sm font-bold opacity-80 hover:opacity-100 transition-all duration-200 border-2 border-white shadow-lg z-20"
                            onClick={(e) => {
                              e.stopPropagation();
                              setCustomBlurAreas(prev => prev.filter(a => a.id !== area.id));
                              console.log('🗑️ Deleted custom area:', area.id);
                              // Auto-save handled by useEffect
                            }}
                            title="Delete custom area"
                          >
                            ×
                          </button>

                          {/* Corner Resize Handles */}
                          <div
                            className="absolute -bottom-1 -right-1 w-4 h-4 bg-purple-600 rounded-full cursor-se-resize opacity-0 group-hover:opacity-100 transition-opacity border-2 border-white shadow-lg z-10"
                            onMouseDown={(e) => {
                              e.stopPropagation();
                              console.log('🔧 SE resize handle clicked');
                              handleCustomAreaResize(area.id, 'se', e);
                            }}
                          />
                          <div
                            className="absolute -top-1 -right-1 w-4 h-4 bg-purple-600 rounded-full cursor-ne-resize opacity-0 group-hover:opacity-100 transition-opacity border-2 border-white shadow-lg z-10"
                            onMouseDown={(e) => {
                              e.stopPropagation();
                              console.log('🔧 NE resize handle clicked');
                              handleCustomAreaResize(area.id, 'ne', e);
                            }}
                          />
                          <div
                            className="absolute -bottom-1 -left-1 w-4 h-4 bg-purple-600 rounded-full cursor-sw-resize opacity-0 group-hover:opacity-100 transition-opacity border-2 border-white shadow-lg z-10"
                            onMouseDown={(e) => {
                              e.stopPropagation();
                              console.log('🔧 SW resize handle clicked');
                              handleCustomAreaResize(area.id, 'sw', e);
                            }}
                          />
                          <div
                            className="absolute -top-1 -left-1 w-4 h-4 bg-purple-600 rounded-full cursor-nw-resize opacity-0 group-hover:opacity-100 transition-opacity border-2 border-white shadow-lg z-10"
                            onMouseDown={(e) => {
                              e.stopPropagation();
                              console.log('🔧 NW resize handle clicked');
                              handleCustomAreaResize(area.id, 'nw', e);
                            }}
                          />

                          {/* Edge Resize Handles */}
                          {/* Top edge */}
                          <div
                            className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-6 h-3 bg-purple-600 rounded cursor-n-resize opacity-0 group-hover:opacity-100 transition-opacity border border-white shadow-lg z-10"
                            onMouseDown={(e) => {
                              e.stopPropagation();
                              console.log('🔧 N resize handle clicked');
                              handleCustomAreaResize(area.id, 'n', e);
                            }}
                          />
                          {/* Bottom edge */}
                          <div
                            className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-3 bg-purple-600 rounded cursor-s-resize opacity-0 group-hover:opacity-100 transition-opacity border border-white shadow-lg z-10"
                            onMouseDown={(e) => {
                              e.stopPropagation();
                              console.log('🔧 S resize handle clicked');
                              handleCustomAreaResize(area.id, 's', e);
                            }}
                          />
                          {/* Left edge */}
                          <div
                            className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-3 h-6 bg-purple-600 rounded cursor-w-resize opacity-0 group-hover:opacity-100 transition-opacity border border-white shadow-lg z-10"
                            onMouseDown={(e) => {
                              e.stopPropagation();
                              console.log('🔧 W resize handle clicked');
                              handleCustomAreaResize(area.id, 'w', e);
                            }}
                          />
                          {/* Right edge */}
                          <div
                            className="absolute -right-1 top-1/2 transform -translate-y-1/2 w-3 h-6 bg-purple-600 rounded cursor-e-resize opacity-0 group-hover:opacity-100 transition-opacity border border-white shadow-lg z-10"
                            onMouseDown={(e) => {
                              e.stopPropagation();
                              console.log('🔧 E resize handle clicked');
                              handleCustomAreaResize(area.id, 'e', e);
                            }}
                          />

                          {/* Center text */}
                          <div className="absolute inset-0 flex items-center justify-center text-purple-600 text-xs font-semibold opacity-0 group-hover:opacity-100 transition-opacity">
                            Drag to move
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
                  </div>
                </div>

                {/* Draggable Multi-Image Navigation Panel */}
                {!loadingImages && allUserImages.length > 1 && (
                  <div
                    className="absolute z-20 bg-black/90 backdrop-blur-sm rounded-lg border border-white/20 shadow-2xl cursor-move"
                    style={{
                      left: `50%`,
                      bottom: `20px`,
                      transform: `translateX(-50%) translateX(${panelPosition.x}px) translateY(${panelPosition.y}px)`,
                      maxWidth: '400px',
                      zIndex: 30
                    }}
                    onMouseDown={(e) => {
                      e.stopPropagation();
                      handleMouseDown(e);
                    }}
                  >
                    {/* Drag Handle */}
                    <div className="flex items-center justify-center py-1 border-b border-white/10">
                      <div className="flex space-x-1">
                        <div className="w-1 h-1 bg-white/40 rounded-full"></div>
                        <div className="w-1 h-1 bg-white/40 rounded-full"></div>
                        <div className="w-1 h-1 bg-white/40 rounded-full"></div>
                        <div className="w-1 h-1 bg-white/40 rounded-full"></div>
                        <div className="w-1 h-1 bg-white/40 rounded-full"></div>
                      </div>
                    </div>

                    <div className="px-4 py-3">
                      <div className="flex items-center space-x-3">
                        <span className="text-white/70 text-xs font-medium whitespace-nowrap">
                          {currentImageIndex + 1} of {allUserImages.length}
                        </span>

                        {/* Navigation Arrows */}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            const prevIndex = currentImageIndex > 0 ? currentImageIndex - 1 : allUserImages.length - 1;
                            const prevImage = allUserImages[prevIndex];
                            if (prevImage) navigateToImage(prevImage);
                          }}
                          className="p-1 rounded hover:bg-white/20 transition-colors"
                          title="Previous image"
                        >
                          <ChevronLeft className="h-4 w-4 text-white/70" />
                        </button>

                        <div className="flex space-x-1 max-w-xs overflow-x-auto scrollbar-hide">
                          {allUserImages.map((image, index) => (
                            <button
                              key={image.id}
                              onClick={(e) => {
                                e.stopPropagation();
                                navigateToImage(image);
                              }}
                              className={`relative flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                                image.id === mediaFile.id
                                  ? 'border-blue-400 ring-2 ring-blue-400/50 scale-110'
                                  : 'border-white/20 hover:border-white/40 opacity-60 hover:opacity-80 hover:scale-105'
                              }`}
                              title={image.original_filename}
                            >
                              <img
                                src={(image as any).url || image.metadata?.originalUrl || ''}
                                alt={image.original_filename}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  // Fallback to a placeholder if image fails to load
                                  e.currentTarget.style.display = 'none';
                                }}
                              />
                              {image.id === mediaFile.id && (
                                <div className="absolute inset-0 bg-blue-400/20 flex items-center justify-center">
                                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                </div>
                              )}
                            </button>
                          ))}
                        </div>

                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            const nextIndex = currentImageIndex < allUserImages.length - 1 ? currentImageIndex + 1 : 0;
                            const nextImage = allUserImages[nextIndex];
                            if (nextImage) navigateToImage(nextImage);
                          }}
                          className="p-1 rounded hover:bg-white/20 transition-colors"
                          title="Next image"
                        >
                          <ChevronRight className="h-4 w-4 text-white/70" />
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Right Sidebar - Matching dashboard design */}
        <div className="w-80 bg-[#0f1629] border-l border-[#1e3a6f] flex flex-col">
          <div className="flex-1 overflow-y-auto">
            {/* Image Tools */}
            {imagePreview && (
              <div className="p-2 border-b border-[#1e3a6f]">
                <h3 className="text-xs font-semibold text-gray-300 mb-2">Image Tools</h3>

                {/* Zoom Control - Compressed */}
                <div className="mb-2">
                  <label className="text-xs font-medium text-gray-400 mb-1 block">Display Mode</label>
                  <div className="flex rounded-lg bg-[#1e3a6f]/30 p-1">
                    <button
                      onClick={() => {
                        console.log('🔄 Switching to fit mode - preserving all state');
                        setImageZoomMode('fit');
                        // State preservation: All detected objects, custom areas, and processed images are maintained
                      }}
                      className={`flex-1 px-3 py-1.5 text-xs font-medium rounded-md transition-colors ${
                        imageZoomMode === 'fit'
                          ? 'bg-purple-600 text-white'
                          : 'text-gray-400 hover:text-white hover:bg-[#1e3a6f]/50'
                      }`}
                    >
                      Fit to View
                    </button>
                    <button
                      onClick={() => {
                        console.log('🔄 Switching to actual size mode - preserving all state');
                        setImageZoomMode('actual');
                        // State preservation: All detected objects, custom areas, and processed images are maintained
                      }}
                      className={`flex-1 px-3 py-1.5 text-xs font-medium rounded-md transition-colors ${
                        imageZoomMode === 'actual'
                          ? 'bg-purple-600 text-white'
                          : 'text-gray-400 hover:text-white hover:bg-[#1e3a6f]/50'
                      }`}
                    >
                      Actual Size
                    </button>
                  </div>
                </div>

                <div className="space-y-2">
                  {/* Detect Button */}
                  <button
                    onClick={detectOnly}
                    disabled={!canDetect() || isProcessing}
                    className={`w-full flex items-center justify-center space-x-2 px-3 py-2 rounded text-sm font-medium transition-colors ${
                      !canDetect() || isProcessing
                        ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700 text-white'
                    }`}
                  >
                    {isProcessing ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Target className="h-4 w-4" />
                    )}
                    <span>{isProcessing ? 'Detecting...' : 'Detect'}</span>
                  </button>

                  {/* Single Blur Button - Blurs ALL (detected objects + custom areas) */}
                  <button
                    onClick={() => {
                      console.log('🎯 SINGLE BLUR BUTTON CLICKED!');
                      console.log('📊 Will blur ALL content:', {
                        detectedObjects: detectedObjects.filter(obj => obj.selected).length,
                        customAreas: customBlurAreas.filter(area => area.visible).length,
                        blurIntensity,
                        blurType
                      });
                      blurAllDetectedObjects(); // This function now handles both types
                    }}
                    disabled={!hasBlurrableObjects() || isProcessing}
                    className={`w-full flex items-center justify-center space-x-2 px-3 py-2 rounded text-sm font-medium transition-colors ${
                      !hasBlurrableObjects() || isProcessing
                        ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        : 'bg-green-600 hover:bg-green-700 text-white'
                    }`}
                  >
                    {isProcessing ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Zap className="h-4 w-4" />
                    )}
                    <span>{isProcessing ? 'Blurring...' : 'Blur All'}</span>
                  </button>

                  {/* Reset to Original */}
                  {processedImageUrl && (
                    <button
                      onClick={() => {
                        console.log('🔄 Resetting to original image');
                        setProcessedImageUrl('');
                      }}
                      className="w-full flex items-center justify-center space-x-2 px-3 py-2 bg-[#1e3a6f] hover:bg-[#2563eb] text-white rounded text-sm font-medium transition-colors"
                    >
                      <span>🔄 Show Original</span>
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Studio Actions - Compressed */}
            <div className="p-2 border-b border-[#1e3a6f]">
              <div className="space-y-2">

                {/* Action Buttons */}
                <div className="flex gap-2">
                  <button
                    onClick={handleDoneClick}
                    disabled={!processedImageUrl}
                    className="w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg text-sm font-medium transition-colors"
                  >
                    Done
                  </button>
                </div>
              </div>
            </div>

            {/* Custom Objects - Compressed */}
            <div className="p-2 border-b border-[#1e3a6f]">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white text-xs font-medium">Custom Objects</h3>
                <span className="text-xs text-gray-400">({customBlurAreas.length})</span>
              </div>

              {customBlurAreas.length > 0 ? (
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {customBlurAreas.map((area) => (
                    <div
                      key={area.id}
                      className="flex items-center justify-between p-2 bg-purple-600/20 rounded-lg border border-purple-600/30"
                    >
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 rounded-full bg-purple-500" />
                        <span className="text-white text-xs font-medium">
                          Custom #{area.id}
                        </span>
                      </div>

                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => toggleCustomAreaVisibility(area.id)}
                          className={`p-1 rounded transition-colors ${
                            area.visible
                              ? 'text-green-400 hover:text-green-300'
                              : 'text-gray-500 hover:text-gray-400'
                          }`}
                          title={area.visible ? 'Hide custom area' : 'Show custom area'}
                        >
                          {area.visible ? <Eye className="w-3 h-3" /> : <EyeOff className="w-3 h-3" />}
                        </button>
                        <button
                          onClick={() => {
                            setCustomBlurAreas(prev => prev.filter(a => a.id !== area.id));
                            console.log('🗑️ Deleted custom area from sidebar:', area.id);
                            // Auto-save handled by useEffect
                          }}
                          className="p-1 text-red-400 hover:text-red-300 transition-colors"
                          title="Delete custom area"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-400 text-xs">No custom areas created</p>
              )}
            </div>

            {/* Detected Objects List - Compressed */}
            <div className="p-2 border-b border-[#1e3a6f]">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white text-xs font-medium">Detected Objects</h3>
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-400">({detectedObjects.length})</span>
                  {allDetectedObjects.length > 0 && (
                    <button
                      onClick={clearAllDetections}
                      className="text-xs text-red-400 hover:text-red-300 transition-colors"
                      title="Clear all detections"
                    >
                      Clear All
                    </button>
                  )}

                </div>
              </div>

              {detectedObjects.length > 0 ? (
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {detectedObjects.map((obj) => (
                    <div
                      key={obj.id}
                      className="flex items-center justify-between p-2 bg-[#1e3a6f]/30 rounded-lg border border-[#1e3a6f]"
                    >
                      <div className="flex items-center space-x-2 flex-1">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: obj.color }}
                        />
                        <div className="flex-1 min-w-0">
                          <p className="text-white text-xs font-medium truncate">
                            {obj.label}
                          </p>
                          <p className="text-gray-400 text-xs">
                            {obj.confidence}% confidence
                            {obj.prompt && (
                              <span className="ml-1 text-blue-400">• {obj.prompt}</span>
                            )}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => toggleObjectVisibility(obj.id)}
                          className={`p-1 rounded transition-colors ${
                            obj.selected
                              ? 'text-green-400 hover:text-green-300'
                              : 'text-gray-500 hover:text-gray-400'
                          }`}
                          title={obj.selected ? 'Hide object' : 'Show object'}
                        >
                          {obj.selected ? <Eye className="w-3 h-3" /> : <EyeOff className="w-3 h-3" />}
                        </button>
                        <button
                          onClick={() => deleteDetectedObject(obj.id)}
                          className="p-1 text-red-400 hover:text-red-300 transition-colors"
                          title="Delete object"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-400 text-xs">No objects detected yet</p>
              )}
            </div>

            {/* Image details */}
            <div className="p-4">
              <h3 className="text-white text-sm font-medium mb-3">Image details</h3>
              <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-400">File name</span>
                  <span className="text-white">{mediaFile.original_filename || 'ps.17306369'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Size</span>
                  <span className="text-white">24.82 KB</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Duration</span>
                  <span className="text-white">-</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">File type</span>
                  <span className="text-white">JPEG</span>
                </div>
              </div>
            </div>


          </div>
        </div>
      </div>

      {/* Save/Download Dialog */}
      {showSaveDialog && processedImageUrl && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-[#0f1629] border border-[#1e3a6f] rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-white mb-4">Save Your Work</h3>

            {/* Image Preview */}
            <div className="mb-4">
              <div className="relative w-full h-32 bg-[#1e3a6f]/30 rounded-lg overflow-hidden">
                <img
                  src={processedImageUrl}
                  alt="Processed result"
                  className="w-full h-full object-contain"
                />
              </div>
            </div>

            {/* Image Info */}
            <div className="mb-6 text-sm text-[#94a3b8]">
              <p><strong className="text-white">File:</strong> processed_{mediaFile.original_filename}</p>
              <p><strong className="text-white">Original:</strong> {mediaFile.original_filename}</p>
              <p><strong className="text-white">Size:</strong> {(mediaFile.file_size / 1024 / 1024).toFixed(2)} MB</p>
              <p><strong className="text-white">Type:</strong> {mediaFile.mime_type || 'image/jpeg'}</p>
            </div>

            <p className="text-[#94a3b8] text-sm mb-6">
              Choose whether to download the image only or save your processing settings for future access.
            </p>

            {/* Action Buttons */}
            <div className="flex gap-3">
              <button
                onClick={() => setShowSaveDialog(false)}
                className="flex-1 px-4 py-2 bg-[#1e3a6f]/30 hover:bg-[#1e3a6f] text-white rounded-lg text-sm font-medium transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleDownloadOnly}
                className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors"
              >
                Download Only
              </button>
              <button
                onClick={handleSaveAndDownload}
                className="flex-1 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm font-medium transition-colors"
              >
                Save & Download
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Insufficient Credits Modal */}
      <InsufficientCreditsModal
        isOpen={showInsufficientCreditsModal}
        onClose={() => setShowInsufficientCreditsModal(false)}
        currentCredits={insufficientCreditsData.current}
        requiredCredits={insufficientCreditsData.required}
      />

      {/* Laser Border Animation CSS */}
      <style jsx>{`
        .prompt-laser-border {
          position: absolute;
          inset: 0;
          border-radius: 8px;
          padding: 2px;
          background: linear-gradient(
            45deg,
            transparent 30%,
            rgba(139, 92, 246, 0.3) 40%,
            rgba(139, 92, 246, 1) 50%,
            rgba(139, 92, 246, 0.3) 60%,
            transparent 70%
          );
          background-size: 300% 300%;
          animation: laser-glow 4s ease-in-out infinite;
          box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
          z-index: 0;
        }

        .prompt-laser-border::before {
          content: '';
          position: absolute;
          inset: 2px;
          background: #1e3a6f;
          border-radius: 6px;
          z-index: 1;
        }

        @keyframes laser-glow {
          0%, 100% {
            background-position: 0% 0%;
            box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
          }
          25% {
            background-position: 100% 0%;
            box-shadow: 0 0 30px rgba(139, 92, 246, 0.5);
          }
          50% {
            background-position: 100% 100%;
            box-shadow: 0 0 40px rgba(139, 92, 246, 0.7);
          }
          75% {
            background-position: 0% 100%;
            box-shadow: 0 0 30px rgba(139, 92, 246, 0.5);
          }
        }
      `}</style>
    </div>
  );
}