'use client';

import React, { useState, useRef, useEffect } from 'react';
import { ArrowLeft, Play, Pause, Volume2, VolumeX, Maximize, SkipBack, SkipForward } from 'lucide-react';
import { useUser } from '@clerk/nextjs';

interface MediaFile {
  id: string | null;
  original_filename: string;
  file_path: string;
  file_type: string;
  file_size: number;
  mime_type?: string;
  metadata?: {
    originalUrl: string;
  };
  created_at: string;
  upload_date?: string;
  user_id?: string;
}

interface GuardiaVisionVideoStudioProps {
  mediaFile: MediaFile;
  originalVideoUrl: string;
  onBack: () => void;
}

export default function GuardiaVisionVideoStudio({ mediaFile, originalVideoUrl, onBack }: GuardiaVisionVideoStudioProps) {
  const { user } = useUser();
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  // Video player state
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  // Frame analysis state
  const [currentFrame, setCurrentFrame] = useState(0);
  const [totalFrames, setTotalFrames] = useState(0);
  const [isAnalysisMode, setIsAnalysisMode] = useState(false);
  const [extractedFrame, setExtractedFrame] = useState<string | null>(null);
  
  // Detection state (reuse from image studio)
  const [textPrompts, setTextPrompts] = useState<string[]>(['']);
  const [detectedObjects, setDetectedObjects] = useState<any[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [blurType, setBlurType] = useState<'blur' | 'pixelate'>('blur');
  const [blurIntensity, setBlurIntensity] = useState(50);

  // Video event handlers
  const handlePlayPause = () => {
    if (!videoRef.current) return;
    
    if (isPlaying) {
      videoRef.current.pause();
    } else {
      videoRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleTimeUpdate = () => {
    if (!videoRef.current) return;
    setCurrentTime(videoRef.current.currentTime);
    
    // Calculate current frame (assuming 30fps)
    const fps = 30;
    setCurrentFrame(Math.floor(videoRef.current.currentTime * fps));
  };

  const handleLoadedMetadata = () => {
    if (!videoRef.current) return;
    setDuration(videoRef.current.duration);
    
    // Calculate total frames (assuming 30fps)
    const fps = 30;
    setTotalFrames(Math.floor(videoRef.current.duration * fps));
  };

  const handleVolumeChange = (newVolume: number) => {
    if (!videoRef.current) return;
    videoRef.current.volume = newVolume;
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
  };

  const handleMuteToggle = () => {
    if (!videoRef.current) return;
    const newMuted = !isMuted;
    videoRef.current.muted = newMuted;
    setIsMuted(newMuted);
  };

  const handlePlaybackRateChange = (rate: number) => {
    if (!videoRef.current) return;
    videoRef.current.playbackRate = rate;
    setPlaybackRate(rate);
  };

  const handleSeek = (time: number) => {
    if (!videoRef.current) return;
    videoRef.current.currentTime = time;
    setCurrentTime(time);
  };

  // Frame navigation
  const handleFrameStep = (direction: 'forward' | 'backward') => {
    if (!videoRef.current) return;
    
    const fps = 30;
    const frameTime = 1 / fps;
    const newTime = direction === 'forward' 
      ? Math.min(currentTime + frameTime, duration)
      : Math.max(currentTime - frameTime, 0);
    
    handleSeek(newTime);
  };

  // Frame extraction for analysis
  const extractCurrentFrame = () => {
    if (!videoRef.current || !canvasRef.current) return null;
    
    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    if (!ctx) return null;
    
    // Set canvas size to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    
    // Draw current video frame to canvas
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    
    // Convert to base64
    const frameData = canvas.toDataURL('image/jpeg', 0.9);
    setExtractedFrame(frameData);
    return frameData;
  };

  // Toggle analysis mode
  const toggleAnalysisMode = () => {
    if (!isAnalysisMode) {
      // Entering analysis mode - pause video and extract frame
      if (videoRef.current && isPlaying) {
        videoRef.current.pause();
        setIsPlaying(false);
      }
      extractCurrentFrame();
    }
    setIsAnalysisMode(!isAnalysisMode);
  };

  // Format time display
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Keyboard controls
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!videoRef.current) return;
      
      switch (e.key) {
        case ' ':
          e.preventDefault();
          handlePlayPause();
          break;
        case 'ArrowLeft':
          e.preventDefault();
          handleFrameStep('backward');
          break;
        case 'ArrowRight':
          e.preventDefault();
          handleFrameStep('forward');
          break;
        case 'f':
        case 'F':
          e.preventDefault();
          if (document.fullscreenElement) {
            document.exitFullscreen();
          } else {
            videoRef.current.requestFullscreen();
          }
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isPlaying]);

  return (
    <div className="min-h-screen bg-[#0a192f] text-white">
      {/* Header Bar - Same as image studio */}
      <div className="bg-[#0a192f] border-b border-[#1e3a6f] px-3 sm:px-6 py-3 sm:py-4">
        <div className="relative flex items-center">
          <button
            onClick={onBack}
            className="flex items-center space-x-1 sm:space-x-2 text-[#94a3b8] hover:text-white transition-colors px-2 sm:px-3 py-2 rounded-lg hover:bg-[#1e3a6f]"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="text-xs sm:text-sm hidden sm:inline">Back to Dashboard</span>
            <span className="text-xs sm:hidden">Back</span>
          </button>

          <div className="absolute left-1/2 transform -translate-x-1/2 flex items-center space-x-2 sm:space-x-3">
            <img
              src="/photo-de-profil-linkedin.png"
              alt="Guardiavision Logo"
              className="h-8 sm:h-10 w-auto"
            />
            <h1 className="text-sm sm:text-xl font-semibold text-white">
              <span className="hidden sm:inline">Guardia<span className="text-[#22C55E]">Vision</span> Video Studio</span>
              <span className="sm:hidden">Video Studio</span>
            </h1>
          </div>

          <div className="ml-auto">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-400">{user?.firstName || 'User'}</span>
              <div className="w-8 h-8 bg-[#1e3a6f] rounded-full flex items-center justify-center">
                <span className="text-sm font-medium">{user?.firstName?.[0] || 'U'}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Three-Panel Layout - Same structure as image studio */}
      <div className="flex flex-col lg:flex-row h-[calc(100vh-77px)] sm:h-[calc(100vh-89px)]">
        {/* Left Sidebar - Reuse detection controls */}
        <div className="w-full lg:w-80 bg-[#0f1629] border-b lg:border-b-0 lg:border-r border-[#1e3a6f] flex flex-col max-h-[40vh] lg:max-h-none overflow-y-auto lg:overflow-visible">
          {/* Video Analysis Toggle */}
          <div className="p-3 border-b border-[#1e3a6f]">
            <button
              onClick={toggleAnalysisMode}
              className={`w-full px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                isAnalysisMode
                  ? 'bg-purple-600 text-white'
                  : 'bg-[#1e3a6f] text-gray-300 hover:bg-[#2a4a7f]'
              }`}
            >
              {isAnalysisMode ? 'Exit Frame Analysis' : 'Analyze Current Frame'}
            </button>
          </div>

          {/* Detection controls - only show in analysis mode */}
          {isAnalysisMode && (
            <>
              {/* Multiple Detection Prompts */}
              <div className="p-2 lg:p-3 border-b border-[#1e3a6f] relative">
                <div className="mb-1 lg:mb-2 flex items-center justify-between">
                  <label className="text-white text-xs font-medium flex items-center">
                    <span className="text-purple-400 mr-1 text-xs">●</span>
                    <span className="hidden sm:inline">Frame Detection</span>
                    <span className="sm:hidden">Detection</span>
                  </label>
                </div>
                
                <div className="space-y-2">
                  {textPrompts.map((prompt, index) => (
                    <div key={index} className="relative">
                      <input
                        type="text"
                        value={prompt}
                        onChange={(e) => {
                          const newPrompts = [...textPrompts];
                          newPrompts[index] = e.target.value;
                          setTextPrompts(newPrompts);
                        }}
                        placeholder={`Detect in frame ${index + 1} (e.g., person, car)...`}
                        className="w-full px-3 py-2 rounded-lg bg-[#1e3a6f]/30 border border-[#1e3a6f] text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 text-sm"
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Blur Controls */}
              <div className="p-3 border-b border-[#1e3a6f]">
                <label className="text-white text-xs font-medium mb-2 block">Effect Type</label>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setBlurType('blur')}
                    className={`flex-1 px-3 py-2 rounded-lg text-xs font-medium transition-all ${
                      blurType === 'blur'
                        ? 'bg-purple-600 text-white'
                        : 'bg-[#1e3a6f] text-gray-300 hover:bg-[#2a4a7f]'
                    }`}
                  >
                    Blur
                  </button>
                  <button
                    onClick={() => setBlurType('pixelate')}
                    className={`flex-1 px-3 py-2 rounded-lg text-xs font-medium transition-all ${
                      blurType === 'pixelate'
                        ? 'bg-purple-600 text-white'
                        : 'bg-[#1e3a6f] text-gray-300 hover:bg-[#2a4a7f]'
                    }`}
                  >
                    Pixelate
                  </button>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Main Content Area - Video Player */}
        <div className="flex-1 bg-[#0a192f] flex flex-col min-h-[60vh] lg:min-h-0">
          <div className="flex-1 relative">
            {/* Video Player */}
            <video
              ref={videoRef}
              src={originalVideoUrl}
              className="w-full h-full object-contain"
              onTimeUpdate={handleTimeUpdate}
              onLoadedMetadata={handleLoadedMetadata}
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
            />

            {/* Hidden canvas for frame extraction */}
            <canvas ref={canvasRef} className="hidden" />

            {/* Video Controls Overlay */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
              {/* Progress Bar */}
              <div className="mb-4">
                <input
                  type="range"
                  min="0"
                  max={duration}
                  value={currentTime}
                  onChange={(e) => handleSeek(Number(e.target.value))}
                  className="w-full h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                />
              </div>

              {/* Control Buttons */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {/* Play/Pause */}
                  <button
                    onClick={handlePlayPause}
                    className="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
                  >
                    {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
                  </button>

                  {/* Frame Step */}
                  <button
                    onClick={() => handleFrameStep('backward')}
                    className="p-1 rounded hover:bg-white/20 transition-colors"
                  >
                    <SkipBack className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleFrameStep('forward')}
                    className="p-1 rounded hover:bg-white/20 transition-colors"
                  >
                    <SkipForward className="h-4 w-4" />
                  </button>

                  {/* Time Display */}
                  <span className="text-sm">
                    {formatTime(currentTime)} / {formatTime(duration)}
                  </span>
                  <span className="text-xs text-gray-400">
                    Frame {currentFrame.toLocaleString()} / {totalFrames.toLocaleString()}
                  </span>
                </div>

                <div className="flex items-center space-x-4">
                  {/* Playback Speed */}
                  <select
                    value={playbackRate}
                    onChange={(e) => handlePlaybackRateChange(Number(e.target.value))}
                    className="bg-black/50 text-white text-sm rounded px-2 py-1"
                  >
                    <option value={0.25}>0.25x</option>
                    <option value={0.5}>0.5x</option>
                    <option value={1}>1x</option>
                    <option value={2}>2x</option>
                    <option value={4}>4x</option>
                  </select>

                  {/* Volume */}
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={handleMuteToggle}
                      className="p-1 rounded hover:bg-white/20 transition-colors"
                    >
                      {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                    </button>
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={volume}
                      onChange={(e) => handleVolumeChange(Number(e.target.value))}
                      className="w-16 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                    />
                  </div>

                  {/* Fullscreen */}
                  <button
                    onClick={() => videoRef.current?.requestFullscreen()}
                    className="p-1 rounded hover:bg-white/20 transition-colors"
                  >
                    <Maximize className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
