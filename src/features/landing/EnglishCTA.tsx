import Link from 'next/link';
import { ShieldCheck } from '@/components/icons/ShieldCheck';

import { buttonVariants } from '@/components/ui/buttonVariants';
import { CustomCTABanner } from '@/features/landing/CustomCTABanner';
import { Section } from '@/features/landing/Section';

export const EnglishCTA = () => {
  return (
    <Section>
      <CustomCTABanner
        title="Ready to get started?"
        description="Join thousands of users who trust our service for their privacy needs."
        buttons={(
          <div className="group relative">
            <div className="rainbow-border rounded-full"></div>
            <Link
              className={buttonVariants({ variant: 'default', size: 'lg', className: 'bg-green-400 hover:bg-green-500 text-navy relative' })}
              href="/sign-up"
            >
              <ShieldCheck className="mr-2 size-5" />
              Start for free
            </Link>
          </div>
        )}
      />
    </Section>
  );
};
