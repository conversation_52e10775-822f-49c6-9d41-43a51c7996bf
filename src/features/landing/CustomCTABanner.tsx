export const CustomCTABanner = (props: {
  title: string;
  description: string;
  buttons: React.ReactNode;
}) => (
  <div
    className="rounded-xl px-6 py-10 text-center relative overflow-hidden"
    style={{
      position: 'relative',
    }}
  >
    {/* Background Image */}
    <div
      className="absolute inset-0 bg-cover bg-center z-0"
      style={{
        backgroundImage: 'url(/photo-de-couverture-linkedin.png)',
        filter: 'brightness(0.7)',
      }}
    />
    
    {/* Content with dark overlay */}
    <div className="relative z-10">
      <div className="text-3xl font-bold text-white">
        {props.title}
      </div>

      <div className="mt-2 text-lg font-medium text-gray-200">
        {props.description}
      </div>

      <div className="mt-6">{props.buttons}</div>
    </div>
  </div>
);
