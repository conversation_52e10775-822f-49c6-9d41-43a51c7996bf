import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { ShieldCheck } from '@/components/icons/ShieldCheck';

import { CustomCTABanner } from '@/features/landing/CustomCTABanner';
import { Section } from '@/features/landing/Section';

export const CTA = () => {
  const t = useTranslations('CTA');

  return (
    <Section>
      <CustomCTABanner
        title={t('title')}
        description={t('description')}
        buttons={(
          <Link
            className="green-neon-border relative inline-flex items-center justify-center rounded-full bg-green-400 px-8 py-4 text-lg font-semibold text-navy transition-all duration-300 hover:bg-green-500 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2 focus:ring-offset-navy z-10"
            href="/sign-up"
          >
            <ShieldCheck className="mr-2 size-5" />
            {t('button_text')}
          </Link>
        )}
      />
    </Section>
  );
};
