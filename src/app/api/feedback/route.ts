import { type NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const { email, feedback, timestamp } = await request.json();

    // Validate inputs
    if (!email || !feedback) {
      return NextResponse.json(
        { error: 'Email and feedback are required' },
        { status: 400 },
      );
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email address' },
        { status: 400 },
      );
    }

    // Message length validation (max 500 characters)
    if (feedback.length > 500) {
      return NextResponse.json(
        { error: 'Message must be 500 characters or less' },
        { status: 400 },
      );
    }

    // Get current user ID from <PERSON> (if logged in)
    const { userId } = await auth();

    // Initialize Supabase client
    const supabase = createServiceRoleSupabaseClient();

    // Determine if user is a paying customer
    let isPayingCustomer = false;
    let userIdToStore = userId || null;

    if (userId) {
      // User is logged in - check their subscription_type
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('subscription_type')
        .eq('id', userId)
        .single();

      if (!userError && user) {
        isPayingCustomer = user.subscription_type !== 'Free';
        console.log(`👤 Logged-in user ${userId}: ${user.subscription_type} plan (paying: ${isPayingCustomer})`);
      }
    } else {
      // User not logged in - check if email exists in users table
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('id, subscription_type')
        .eq('email', email)
        .single();

      if (!userError && user) {
        isPayingCustomer = user.subscription_type !== 'Free';
        userIdToStore = user.id; // Store the user ID if we found them by email
        console.log(`📧 Anonymous user found by email: ${user.subscription_type} plan (paying: ${isPayingCustomer})`);
      } else {
        console.log(`📧 Anonymous user not found in database, treating as non-paying customer`);
      }
    }

    // Store feedback in database
    const { error: insertError } = await supabase
      .from('feedback')
      .insert({
        email,
        message: feedback,
        is_paying_customer: isPayingCustomer,
        user_id: userIdToStore,
      });

    if (insertError) {
      console.error('❌ Error storing feedback in database:', insertError);
      // Continue to email sending even if database insert fails
    } else {
      console.log('✅ Feedback stored in database successfully');
    }

    // Log feedback to console
    console.log('=== NEW FEEDBACK ===');
    console.log('From:', email);
    console.log('User ID:', userIdToStore || 'Anonymous');
    console.log('Paying Customer:', isPayingCustomer);
    console.log('Timestamp:', new Date(timestamp).toLocaleString());
    console.log('Feedback:', feedback);
    console.log('===================');

    // Send email using Nodemailer (universal email solution)
    try {
      const nodemailer = require('nodemailer');

      // Create transporter (you can use Gmail, SendGrid, Mailgun, etc.)
      // For production, use environment variables for credentials
      const transporter = nodemailer.createTransporter({
        host: process.env.SMTP_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: false, // true for 465, false for other ports
        auth: {
          user: process.env.SMTP_USER, // Your email
          pass: process.env.SMTP_PASS, // Your app password
        },
      });

      // Email HTML content
      const htmlContent = `
<!DOCTYPE html>
<html>
<head>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }
    .content { background: #f9fafb; padding: 30px; border: 1px solid #e5e7eb; }
    .footer { background: #f3f4f6; padding: 15px; text-align: center; font-size: 12px; color: #6b7280; border-radius: 0 0 8px 8px; }
    .label { font-weight: bold; color: #4b5563; }
    .value { margin-bottom: 15px; padding: 10px; background: white; border-radius: 4px; }
    .badge { display: inline-block; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: bold; margin-left: 8px; }
    .badge-paying { background: #10b981; color: white; }
    .badge-free { background: #6b7280; color: white; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h2 style="margin: 0;">🎯 New Feedback Received</h2>
      <p style="margin: 5px 0 0 0; opacity: 0.9;">Guardiavision Feedback Widget</p>
    </div>
    <div class="content">
      <div class="value">
        <span class="label">From:</span><br>
        <a href="mailto:${email}" style="color: #667eea;">${email}</a>
        ${isPayingCustomer ? '<span class="badge badge-paying">💎 PAYING CUSTOMER</span>' : '<span class="badge badge-free">Free User</span>'}
      </div>
      ${userIdToStore ? `
      <div class="value">
        <span class="label">User ID:</span><br>
        ${userIdToStore}
      </div>
      ` : ''}
      <div class="value">
        <span class="label">Submitted:</span><br>
        ${new Date(timestamp).toLocaleString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })}
      </div>
      <div class="value">
        <span class="label">Feedback:</span><br>
        <p style="white-space: pre-wrap; margin: 10px 0 0 0;">${feedback}</p>
      </div>
    </div>
    <div class="footer">
      This email was generated automatically from the Guardiavision feedback widget.
    </div>
  </div>
</body>
</html>
      `.trim();

      const textContent = `
New Feedback Received from Guardiavision

From: ${email}
${isPayingCustomer ? '💎 PAYING CUSTOMER' : 'Free User'}
${userIdToStore ? `User ID: ${userIdToStore}` : 'Anonymous User'}
Time: ${new Date(timestamp).toLocaleString()}

Feedback:
${feedback}

---
This feedback was submitted via the Guardiavision feedback widget.
      `.trim();

      // Send email
      const info = await transporter.sendMail({
        from: `"Guardiavision Feedback" <${process.env.SMTP_USER}>`,
        to: '<EMAIL>',
        replyTo: email,
        subject: `🎯 New Feedback from ${email}${isPayingCustomer ? ' 💎 PAYING' : ''}`,
        text: textContent,
        html: htmlContent,
      });

      console.log('✅ Email sent successfully:', info.messageId);

      return NextResponse.json(
        {
          success: true,
          message: 'Feedback submitted successfully',
        },
        { status: 200 },
      );
    } catch (emailError: any) {
      console.error('❌ Email sending failed:', emailError);

      // Still return success to user, but log the error
      // The feedback is logged to console and database as backup
      return NextResponse.json(
        {
          success: true,
          message: 'Feedback received',
          note: 'Email notification may be delayed',
        },
        { status: 200 },
      );
    }
  } catch (error) {
    console.error('Error processing feedback:', error);
    return NextResponse.json(
      { error: 'Failed to process feedback' },
      { status: 500 },
    );
  }
}
