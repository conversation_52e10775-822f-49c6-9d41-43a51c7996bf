import { type NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { email, feedback, timestamp } = await request.json();

    // Validate inputs
    if (!email || !feedback) {
      return NextResponse.json(
        { error: 'Email and feedback are required' },
        { status: 400 },
      );
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email address' },
        { status: 400 },
      );
    }

    // Log feedback to console
    console.log('=== NEW FEEDBACK ===');
    console.log('From:', email);
    console.log('Timestamp:', new Date(timestamp).toLocaleString());
    console.log('Feedback:', feedback);
    console.log('===================');

    // Send email using Nodemailer (universal email solution)
    try {
      const nodemailer = require('nodemailer');

      // Create transporter (you can use Gmail, SendGrid, Mailgun, etc.)
      // For production, use environment variables for credentials
      const transporter = nodemailer.createTransporter({
        host: process.env.SMTP_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: false, // true for 465, false for other ports
        auth: {
          user: process.env.SMTP_USER, // Your email
          pass: process.env.SMTP_PASS, // Your app password
        },
      });

      // Email HTML content
      const htmlContent = `
<!DOCTYPE html>
<html>
<head>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }
    .content { background: #f9fafb; padding: 30px; border: 1px solid #e5e7eb; }
    .footer { background: #f3f4f6; padding: 15px; text-align: center; font-size: 12px; color: #6b7280; border-radius: 0 0 8px 8px; }
    .label { font-weight: bold; color: #4b5563; }
    .value { margin-bottom: 15px; padding: 10px; background: white; border-radius: 4px; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h2 style="margin: 0;">🎯 New Feedback Received</h2>
      <p style="margin: 5px 0 0 0; opacity: 0.9;">Guardiavision Feedback Widget</p>
    </div>
    <div class="content">
      <div class="value">
        <span class="label">From:</span><br>
        <a href="mailto:${email}" style="color: #667eea;">${email}</a>
      </div>
      <div class="value">
        <span class="label">Submitted:</span><br>
        ${new Date(timestamp).toLocaleString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })}
      </div>
      <div class="value">
        <span class="label">Feedback:</span><br>
        <p style="white-space: pre-wrap; margin: 10px 0 0 0;">${feedback}</p>
      </div>
    </div>
    <div class="footer">
      This email was generated automatically from the Guardiavision feedback widget.
    </div>
  </div>
</body>
</html>
      `.trim();

      const textContent = `
New Feedback Received from Guardiavision

From: ${email}
Time: ${new Date(timestamp).toLocaleString()}

Feedback:
${feedback}

---
This feedback was submitted via the Guardiavision feedback widget.
      `.trim();

      // Send email
      const info = await transporter.sendMail({
        from: `"Guardiavision Feedback" <${process.env.SMTP_USER}>`,
        to: '<EMAIL>',
        replyTo: email,
        subject: `🎯 New Feedback from ${email}`,
        text: textContent,
        html: htmlContent,
      });

      console.log('✅ Email sent successfully:', info.messageId);

      return NextResponse.json(
        {
          success: true,
          message: 'Feedback submitted successfully',
        },
        { status: 200 },
      );
    } catch (emailError: any) {
      console.error('❌ Email sending failed:', emailError);

      // Still return success to user, but log the error
      // The feedback is logged to console as backup
      return NextResponse.json(
        {
          success: true,
          message: 'Feedback received',
          note: 'Email notification may be delayed',
        },
        { status: 200 },
      );
    }
  } catch (error) {
    console.error('Error processing feedback:', error);
    return NextResponse.json(
      { error: 'Failed to process feedback' },
      { status: 500 },
    );
  }
}
